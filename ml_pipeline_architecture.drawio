<mxfile host="app.diagrams.net" modified="2024-01-01T00:00:00.000Z" agent="5.0" etag="def456" version="24.0.0">
  <diagram name="ML Pipeline Architecture" id="ml-pipeline">
    <mxGraphModel dx="1422" dy="994" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1200" pageHeight="1800" math="0" shadow="0">
      <root>
        <mxCell id="0"/>
        <mxCell id="1" parent="0"/>
        
        <!-- Title -->
        <mxCell id="title" value="机器学习模型训练架构流程" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=18;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="450" y="20" width="300" height="30" as="geometry"/>
        </mxCell>
        
        <!-- Configuration Layer -->
        <mxCell id="config-layer" value="配置层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="80" width="1100" height="40" as="geometry"/>
        </mxCell>
        
        <mxCell id="model-config" value="模型参数配置&#xa;(model_code.json)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="300" y="140" width="140" height="50" as="geometry"/>
        </mxCell>
        
        <mxCell id="dataset-config" value="数据集配置&#xa;(dataset_code.json)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="530" y="140" width="140" height="50" as="geometry"/>
        </mxCell>
        
        <mxCell id="config-manager" value="ConfigManager&#xa;配置管理器" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="760" y="140" width="120" height="50" as="geometry"/>
        </mxCell>
        
        <!-- Data Loading Stage -->
        <mxCell id="data-loading" value="1. 数据加载阶段" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="250" width="1100" height="40" as="geometry"/>
        </mxCell>
        
        <mxCell id="data-loader" value="DataLoader&#xa;数据加载器" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="100" y="310" width="120" height="60" as="geometry"/>
        </mxCell>
        
        <mxCell id="dataset-path" value="数据集路径&#xa;dataset_code" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="260" y="310" width="100" height="60" as="geometry"/>
        </mxCell>
        
        <mxCell id="train-data" value="训练数据&#xa;(train_dates)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="400" y="310" width="100" height="60" as="geometry"/>
        </mxCell>
        
        <mxCell id="test-data" value="测试数据&#xa;(test_dates)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="540" y="310" width="100" height="60" as="geometry"/>
        </mxCell>
        
        <mxCell id="eval-data" value="评估数据&#xa;(evaluate_file)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="680" y="310" width="100" height="60" as="geometry"/>
        </mxCell>
        
        <mxCell id="extra-datasets" value="额外数据集&#xa;(extra_datasets)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="820" y="310" width="100" height="60" as="geometry"/>
        </mxCell>
        
        <!-- Data Preprocessing Stage -->
        <mxCell id="preprocessing" value="2. 数据预处理阶段" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="420" width="1100" height="40" as="geometry"/>
        </mxCell>
        
        <mxCell id="preprocessor" value="DataPreprocessor&#xa;数据预处理器" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="100" y="480" width="140" height="60" as="geometry"/>
        </mxCell>
        
        <mxCell id="feature-preprocess" value="特征预处理&#xa;(基于数据集配置)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="340" y="480" width="130" height="60" as="geometry"/>
        </mxCell>
        
        <mxCell id="model-feature-preprocess" value="模型特征预处理&#xa;(基于模型配置)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="570" y="480" width="130" height="60" as="geometry"/>
        </mxCell>
        
        <mxCell id="label-process" value="标签处理&#xa;(purchase_labels)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="800" y="480" width="120" height="60" as="geometry"/>
        </mxCell>
        
        <!-- Feature Engineering Stage -->
        <mxCell id="feature-engineering" value="3. 特征工程阶段" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="590" width="1100" height="40" as="geometry"/>
        </mxCell>
        
        <mxCell id="feature-builder" value="FeatureBuilder&#xa;特征构建器" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="100" y="650" width="120" height="60" as="geometry"/>
        </mxCell>
        
        <mxCell id="train-test-split" value="训练测试分割&#xa;split_train_test" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="280" y="650" width="120" height="60" as="geometry"/>
        </mxCell>
        
        <mxCell id="dataset-generation" value="数据集生成&#xa;generate_dataset" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="460" y="650" width="120" height="60" as="geometry"/>
        </mxCell>
        
        <mxCell id="embedding-features" value="嵌入特征处理&#xa;(embedding_data)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="640" y="650" width="120" height="60" as="geometry"/>
        </mxCell>
        
        <mxCell id="multitask-support" value="多任务学习支持&#xa;(use_multitask)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="820" y="650" width="120" height="60" as="geometry"/>
        </mxCell>
        
        <!-- Model Training Stage -->
        <mxCell id="model-training" value="4. 模型训练阶段" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="760" width="1100" height="40" as="geometry"/>
        </mxCell>
        
        <mxCell id="model-trainer" value="ModelTrainer&#xa;模型训练器" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="100" y="865" width="120" height="60" as="geometry"/>
        </mxCell>
        
        <mxCell id="model-building" value="模型构建&#xa;build_model" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="280" y="865" width="100" height="60" as="geometry"/>
        </mxCell>
        
        <mxCell id="cross-layer" value="Cross Layer&#xa;交叉层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f0f0;strokeColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="440" y="820" width="110" height="40" as="geometry"/>
        </mxCell>
        
        <mxCell id="time-attention" value="Time Attention&#xa;时间注意力" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f0f0;strokeColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="440" y="870" width="110" height="40" as="geometry"/>
        </mxCell>
        
        <mxCell id="transformer" value="Transformer&#xa;架构选择" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f0f0;strokeColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="590" y="820" width="110" height="40" as="geometry"/>
        </mxCell>
        
        <mxCell id="loss-function" value="损失函数&#xa;(focal/weighted)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f0f0;strokeColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="590" y="870" width="110" height="40" as="geometry"/>
        </mxCell>
        
        <mxCell id="training-process" value="训练过程&#xa;(epochs, patience)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="760" y="865" width="120" height="60" as="geometry"/>
        </mxCell>
        
        <!-- Model Inference Stage -->
        <mxCell id="model-inference" value="5. 模型推理阶段" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="970" width="1100" height="40" as="geometry"/>
        </mxCell>
        
        <mxCell id="test-inference" value="测试集推理&#xa;(df_test_m)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="300" y="1030" width="120" height="60" as="geometry"/>
        </mxCell>
        
        <mxCell id="eval-inference" value="评估集推理&#xa;(df_evaluate)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="540" y="1030" width="120" height="60" as="geometry"/>
        </mxCell>
        
        <mxCell id="prediction-results" value="预测结果&#xa;(result column)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="780" y="1030" width="120" height="60" as="geometry"/>
        </mxCell>
        
        <!-- Model Evaluation Stage -->
        <mxCell id="model-evaluation" value="6. 模型评估阶段" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="1140" width="1100" height="40" as="geometry"/>
        </mxCell>
        
        <mxCell id="model-evaluator" value="ModelEvaluator&#xa;模型评估器" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="240" y="1200" width="120" height="60" as="geometry"/>
        </mxCell>
        
        <mxCell id="evaluation-metrics" value="评估指标计算&#xa;(AUC, Precision, Recall)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="490" y="1200" width="150" height="60" as="geometry"/>
        </mxCell>
        
        <mxCell id="evaluation-results" value="评估结果输出&#xa;(evaluation_results)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="770" y="1200" width="140" height="60" as="geometry"/>
        </mxCell>
        
        <!-- Arrows -->
        <mxCell id="arrow1" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=classic;" edge="1" parent="1" source="config-layer" target="data-loading">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        
        <mxCell id="arrow2" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=classic;" edge="1" parent="1" source="data-loading" target="preprocessing">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        
        <mxCell id="arrow3" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=classic;" edge="1" parent="1" source="preprocessing" target="feature-engineering">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        
        <mxCell id="arrow4" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=classic;" edge="1" parent="1" source="feature-engineering" target="model-training">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        
        <mxCell id="arrow5" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=classic;" edge="1" parent="1" source="model-training" target="model-inference">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        
        <mxCell id="arrow6" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=classic;" edge="1" parent="1" source="model-inference" target="model-evaluation">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile> 