"""
增强预处理器 - 集成数据质量检查和智能处理

主要改进：
1. 预处理前后的数据质量对比
2. 智能特征类型推断
3. 自动异常处理策略
4. 预处理效果评估
"""

import pandas as pd
import numpy as np
import logging
import json
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
import warnings
from datetime import datetime

from src.data.data_validator import DataQualityValidator

class EnhancedDataPreprocessor:
    """
    增强版数据预处理器
    
    功能特点：
    - 预处理前后质量对比
    - 智能特征处理策略
    - 自动生成处理报告
    - 支持多种特征类型
    """
    
    def __init__(self, 
                 enable_validation: bool = True,
                 auto_feature_engineering: bool = True,
                 generate_reports: bool = True):
        """
        初始化增强预处理器
        
        Args:
            enable_validation: 是否启用验证
            auto_feature_engineering: 是否自动特征工程
            generate_reports: 是否生成报告
        """
        self.logger = logging.getLogger(__name__)
        self.enable_validation = enable_validation
        self.auto_feature_engineering = auto_feature_engineering
        self.generate_reports = generate_reports
        
        # 初始化组件
        if enable_validation:
            self.validator = DataQualityValidator()
        
        # 处理统计
        self.preprocessing_stats = {}
        self.feature_transformations = {}
        
        self.logger.info("Enhanced preprocessor initialized")
    
    def preprocess_features(self, 
                          df: pd.DataFrame,
                          feature_config: Dict[str, Any],
                          preprocessing_config: Optional[Dict[str, Any]] = None) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """
        增强特征预处理
        
        Args:
            df: 原始数据框
            feature_config: 特征配置
            preprocessing_config: 预处理配置
            
        Returns:
            Tuple[处理后的数据框, 处理报告]
        """
        start_time = datetime.now()
        self.logger.info(f"🔄 开始增强特征预处理：{len(df)} 行，{len(df.columns)} 列")
        
        try:
            # 1. 预处理前的质量检查
            before_report = {}
            if self.enable_validation:
                before_report = self._validate_before_preprocessing(df, feature_config)
            
            # 2. 应用预处理
            processed_df = self._apply_enhanced_preprocessing(df, preprocessing_config or {})
            
            # 3. 智能特征工程
            if self.auto_feature_engineering:
                processed_df = self._apply_auto_feature_engineering(processed_df, feature_config)
            
            # 4. 预处理后的质量检查
            after_report = {}
            if self.enable_validation:
                after_report = self._validate_after_preprocessing(processed_df, feature_config)
            
            # 5. 生成处理报告
            processing_time = (datetime.now() - start_time).total_seconds()
            comprehensive_report = self._generate_preprocessing_report(
                before_report, after_report, df, processed_df, processing_time)
            
            self.logger.info(f"✅ 特征预处理完成：{len(processed_df)} 行，{len(processed_df.columns)} 列")
            
            return processed_df, comprehensive_report
            
        except Exception as e:
            self.logger.error(f"❌ 特征预处理失败：{e}")
            raise
    
    def _validate_before_preprocessing(self, df: pd.DataFrame, 
                                     feature_config: Dict[str, Any]) -> Dict[str, Any]:
        """预处理前验证"""
        self.logger.info("🔍 预处理前质量检查...")
        
        return self.validator.validate_dataframe(
            df, feature_config, "before_preprocessing")
    
    def _validate_after_preprocessing(self, df: pd.DataFrame,
                                    feature_config: Dict[str, Any]) -> Dict[str, Any]:
        """预处理后验证"""
        self.logger.info("🔍 预处理后质量检查...")
        
        return self.validator.validate_dataframe(
            df, feature_config, "after_preprocessing")
    
    def _apply_enhanced_preprocessing(self, df: pd.DataFrame,
                                    preprocessing_config: Dict[str, Any]) -> pd.DataFrame:
        """应用增强预处理"""
        processed_df = df.copy()
        transformations = []
        
        # 1. 数据清理
        processed_df, cleaning_stats = self._enhanced_data_cleaning(processed_df, preprocessing_config)
        transformations.append(("data_cleaning", cleaning_stats))
        
        # 2. 缺失值处理
        processed_df, missing_stats = self._enhanced_missing_value_handling(processed_df, preprocessing_config)
        transformations.append(("missing_value_handling", missing_stats))
        
        # 3. 异常值处理
        processed_df, outlier_stats = self._enhanced_outlier_handling(processed_df, preprocessing_config)
        transformations.append(("outlier_handling", outlier_stats))
        
        # 4. 特征类型优化
        processed_df, type_stats = self._optimize_feature_types(processed_df)
        transformations.append(("type_optimization", type_stats))
        
        # 5. 数据验证
        processed_df, validation_stats = self._validate_processed_data(processed_df)
        transformations.append(("validation", validation_stats))
        
        self.feature_transformations = dict(transformations)
        
        return processed_df
    
    def _enhanced_data_cleaning(self, df: pd.DataFrame,
                              config: Dict[str, Any]) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """增强数据清理"""
        self.logger.info("🧹 执行数据清理...")
        
        cleaned_df = df.copy()
        stats = {"operations": []}
        
        # 删除重复行
        if config.get("remove_duplicates", True):
            before_count = len(cleaned_df)
            cleaned_df = cleaned_df.drop_duplicates()
            after_count = len(cleaned_df)
            
            duplicates_removed = before_count - after_count
            if duplicates_removed > 0:
                stats["operations"].append(f"删除重复行：{duplicates_removed} 行")
                self.logger.info(f"   - 删除重复行：{duplicates_removed} 行")
        
        # 删除全空列
        empty_columns = cleaned_df.columns[cleaned_df.isnull().all()].tolist()
        if empty_columns:
            cleaned_df = cleaned_df.drop(columns=empty_columns)
            stats["operations"].append(f"删除全空列：{len(empty_columns)} 列")
            self.logger.info(f"   - 删除全空列：{empty_columns}")
        
        # 删除常量列
        if config.get("remove_constant_columns", True):
            constant_columns = []
            for col in cleaned_df.columns:
                if cleaned_df[col].nunique() <= 1:
                    constant_columns.append(col)
            
            if constant_columns:
                cleaned_df = cleaned_df.drop(columns=constant_columns)
                stats["operations"].append(f"删除常量列：{len(constant_columns)} 列")
                self.logger.info(f"   - 删除常量列：{constant_columns}")
        
        stats["final_shape"] = cleaned_df.shape
        return cleaned_df, stats
    
    def _enhanced_missing_value_handling(self, df: pd.DataFrame,
                                       config: Dict[str, Any]) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """增强缺失值处理"""
        self.logger.info("🔧 处理缺失值...")
        
        filled_df = df.copy()
        stats = {"strategies": {}, "before_missing": {}, "after_missing": {}}
        
        # 记录处理前的缺失值情况
        stats["before_missing"] = filled_df.isnull().sum().to_dict()
        
        # 获取缺失值处理策略
        missing_strategy = config.get("missing_value_strategy", "intelligent")
        
        if missing_strategy == "intelligent":
            # 智能缺失值处理
            filled_df, strategy_stats = self._intelligent_missing_value_handling(filled_df)
            stats["strategies"] = strategy_stats
        else:
            # 传统缺失值处理
            filled_df = self._traditional_missing_value_handling(filled_df, config)
            stats["strategies"]["method"] = "traditional"
        
        # 记录处理后的缺失值情况
        stats["after_missing"] = filled_df.isnull().sum().to_dict()
        
        # 计算改进效果
        total_before = sum(stats["before_missing"].values())
        total_after = sum(stats["after_missing"].values())
        stats["improvement"] = total_before - total_after
        
        if stats["improvement"] > 0:
            self.logger.info(f"   - 处理缺失值：{stats['improvement']} 个")
        
        return filled_df, stats
    
    def _intelligent_missing_value_handling(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """智能缺失值处理"""
        filled_df = df.copy()
        strategies = {}
        
        for column in filled_df.columns:
            if filled_df[column].isnull().any():
                missing_rate = filled_df[column].isnull().mean()
                
                if missing_rate > 0.5:
                    # 缺失率超过50%，考虑删除列
                    strategies[column] = "high_missing_rate_column"
                    continue
                
                elif filled_df[column].dtype == 'object':
                    # 类别特征：用众数或特殊标记填充
                    mode_value = filled_df[column].mode()
                    if len(mode_value) > 0:
                        filled_df[column] = filled_df[column].fillna(mode_value[0])
                        strategies[column] = f"mode_fill({mode_value[0]})"
                    else:
                        filled_df[column] = filled_df[column].fillna("<MISSING>")
                        strategies[column] = "missing_token"
                
                elif filled_df[column].dtype in ['int64', 'float64']:
                    # 数值特征：根据分布选择策略
                    if missing_rate < 0.1:
                        # 缺失率低：用中位数填充
                        median_value = filled_df[column].median()
                        filled_df[column] = filled_df[column].fillna(median_value)
                        strategies[column] = f"median_fill({median_value})"
                    else:
                        # 缺失率较高：用均值填充
                        mean_value = filled_df[column].mean()
                        filled_df[column] = filled_df[column].fillna(mean_value)
                        strategies[column] = f"mean_fill({mean_value:.2f})"
        
        return filled_df, strategies
    
    def _traditional_missing_value_handling(self, df: pd.DataFrame,
                                          config: Dict[str, Any]) -> pd.DataFrame:
        """传统缺失值处理"""
        filled_df = df.copy()
        
        categorical_token = config.get("categorical_missing_token", "<UNK>")
        numerical_value = config.get("numerical_missing_value", 0)
        
        for column in filled_df.columns:
            if filled_df[column].dtype == 'object':
                filled_df[column] = filled_df[column].fillna(categorical_token)
            else:
                filled_df[column] = filled_df[column].fillna(numerical_value)
        
        return filled_df
    
    def _enhanced_outlier_handling(self, df: pd.DataFrame,
                                 config: Dict[str, Any]) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """增强异常值处理"""
        self.logger.info("🎯 处理异常值...")
        
        processed_df = df.copy()
        stats = {"method": "iqr_and_zscore", "outliers_detected": {}, "outliers_handled": {}}
        
        outlier_threshold = config.get("outlier_threshold", 3.0)
        outlier_method = config.get("outlier_method", "iqr")
        
        numerical_columns = processed_df.select_dtypes(include=[np.number]).columns
        
        for column in numerical_columns:
            if column.startswith('_'):  # 跳过内部列
                continue
            
            outlier_mask = self._detect_outliers(processed_df[column], method=outlier_method, threshold=outlier_threshold)
            outlier_count = outlier_mask.sum()
            
            if outlier_count > 0:
                stats["outliers_detected"][column] = outlier_count
                
                # 处理异常值
                if outlier_count / len(processed_df) < 0.05:  # 异常值比例小于5%
                    # 用分位数替换
                    q75 = processed_df[column].quantile(0.75)
                    q25 = processed_df[column].quantile(0.25)
                    processed_df.loc[outlier_mask, column] = np.where(
                        processed_df.loc[outlier_mask, column] > q75, q75, q25)
                    stats["outliers_handled"][column] = "quantile_replacement"
                else:
                    # 异常值过多，用中位数替换
                    median_value = processed_df[column].median()
                    processed_df.loc[outlier_mask, column] = median_value
                    stats["outliers_handled"][column] = "median_replacement"
        
        total_outliers = sum(stats["outliers_detected"].values())
        if total_outliers > 0:
            self.logger.info(f"   - 处理异常值：{total_outliers} 个")
        
        return processed_df, stats
    
    def _detect_outliers(self, series: pd.Series, method: str = "iqr", threshold: float = 3.0) -> pd.Series:
        """检测异常值"""
        if method == "iqr":
            Q1 = series.quantile(0.25)
            Q3 = series.quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            return (series < lower_bound) | (series > upper_bound)
        
        elif method == "zscore":
            z_scores = np.abs((series - series.mean()) / series.std())
            return z_scores > threshold
        
        else:
            raise ValueError(f"Unknown outlier detection method: {method}")
    
    def _optimize_feature_types(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """优化特征类型"""
        self.logger.info("⚡ 优化特征类型...")
        
        optimized_df = df.copy()
        stats = {"conversions": [], "memory_saved": 0}
        
        before_memory = optimized_df.memory_usage(deep=True).sum()
        
        for column in optimized_df.columns:
            if optimized_df[column].dtype == 'object':
                # 尝试转换为数值类型
                try:
                    converted = pd.to_numeric(optimized_df[column], errors='coerce')
                    if not converted.isnull().all():
                        optimized_df[column] = converted
                        stats["conversions"].append(f"{column}: object -> numeric")
                except:
                    pass
            
            elif optimized_df[column].dtype == 'int64':
                # 整数类型优化
                max_val = optimized_df[column].max()
                min_val = optimized_df[column].min()
                
                if min_val >= 0 and max_val < 255:
                    optimized_df[column] = optimized_df[column].astype('uint8')
                    stats["conversions"].append(f"{column}: int64 -> uint8")
                elif min_val >= -128 and max_val < 127:
                    optimized_df[column] = optimized_df[column].astype('int8')
                    stats["conversions"].append(f"{column}: int64 -> int8")
                elif min_val >= -32768 and max_val < 32767:
                    optimized_df[column] = optimized_df[column].astype('int16')
                    stats["conversions"].append(f"{column}: int64 -> int16")
                elif min_val >= -2147483648 and max_val < 2147483647:
                    optimized_df[column] = optimized_df[column].astype('int32')
                    stats["conversions"].append(f"{column}: int64 -> int32")
            
            elif optimized_df[column].dtype == 'float64':
                # 浮点数类型优化
                optimized_df[column] = pd.to_numeric(optimized_df[column], downcast='float')
                if optimized_df[column].dtype != 'float64':
                    stats["conversions"].append(f"{column}: float64 -> {optimized_df[column].dtype}")
        
        after_memory = optimized_df.memory_usage(deep=True).sum()
        stats["memory_saved"] = before_memory - after_memory
        
        if stats["memory_saved"] > 0:
            self.logger.info(f"   - 内存优化：节省 {stats['memory_saved'] / 1024 / 1024:.2f} MB")
        
        return optimized_df, stats
    
    def _validate_processed_data(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """验证处理后的数据"""
        stats = {"validation_checks": []}
        
        # 基本验证检查
        if df.empty:
            raise ValueError("处理后数据为空")
        
        # 检查无效值
        inf_count = np.isinf(df.select_dtypes(include=[np.number])).sum().sum()
        if inf_count > 0:
            self.logger.warning(f"发现 {inf_count} 个无穷值")
            stats["validation_checks"].append(f"infinite_values: {inf_count}")
        
        # 检查仍有缺失值的列
        missing_columns = df.columns[df.isnull().any()].tolist()
        if missing_columns:
            self.logger.warning(f"以下列仍有缺失值：{missing_columns}")
            stats["validation_checks"].append(f"columns_with_missing: {len(missing_columns)}")
        
        # 检查数据类型一致性
        dtype_issues = []
        for column in df.columns:
            if df[column].dtype == 'object':
                unique_types = set(type(x).__name__ for x in df[column].dropna().unique())
                if len(unique_types) > 1:
                    dtype_issues.append(column)
        
        if dtype_issues:
            stats["validation_checks"].append(f"mixed_type_columns: {len(dtype_issues)}")
        
        if not stats["validation_checks"]:
            stats["validation_checks"].append("all_checks_passed")
            self.logger.info("   - 数据验证：所有检查通过")
        
        return df, stats
    
    def _apply_auto_feature_engineering(self, df: pd.DataFrame,
                                      feature_config: Dict[str, Any]) -> pd.DataFrame:
        """自动特征工程"""
        if not self.auto_feature_engineering:
            return df
        
        self.logger.info("🤖 应用自动特征工程...")
        
        engineered_df = df.copy()
        
        # 1. 创建缺失值指示器
        for column in df.columns:
            if df[column].isnull().any():
                engineered_df[f"{column}_is_missing"] = df[column].isnull().astype(int)
        
        # 2. 数值特征统计
        numerical_columns = df.select_dtypes(include=[np.number]).columns
        if len(numerical_columns) > 1:
            engineered_df['numerical_features_sum'] = df[numerical_columns].sum(axis=1)
            engineered_df['numerical_features_mean'] = df[numerical_columns].mean(axis=1)
            engineered_df['numerical_features_std'] = df[numerical_columns].std(axis=1)
        
        # 3. 类别特征计数
        categorical_columns = df.select_dtypes(include=['object']).columns
        if len(categorical_columns) > 0:
            engineered_df['categorical_features_count'] = df[categorical_columns].count(axis=1)
        
        added_features = len(engineered_df.columns) - len(df.columns)
        if added_features > 0:
            self.logger.info(f"   - 自动生成特征：{added_features} 个")
        
        return engineered_df
    
    def _generate_preprocessing_report(self, before_report: Dict[str, Any],
                                     after_report: Dict[str, Any],
                                     original_df: pd.DataFrame,
                                     processed_df: pd.DataFrame,
                                     processing_time: float) -> Dict[str, Any]:
        """生成预处理报告"""
        report = {
            "summary": {
                "processing_time_seconds": processing_time,
                "original_shape": original_df.shape,
                "processed_shape": processed_df.shape,
                "features_added": processed_df.shape[1] - original_df.shape[1],
                "rows_removed": original_df.shape[0] - processed_df.shape[0]
            },
            "transformations": self.feature_transformations,
            "quality_comparison": {
                "before": before_report,
                "after": after_report,
                "improvement": self._calculate_quality_improvement(before_report, after_report)
            },
            "recommendations": self._generate_preprocessing_recommendations(before_report, after_report)
        }
        
        if self.generate_reports:
            self._save_preprocessing_report(report)
        
        return report
    
    def _calculate_quality_improvement(self, before: Dict[str, Any], after: Dict[str, Any]) -> Dict[str, Any]:
        """计算质量改进"""
        if not before or not after:
            return {}
        
        before_errors = len(before.get("errors", []))
        after_errors = len(after.get("errors", []))
        
        before_warnings = len(before.get("warnings", []))
        after_warnings = len(after.get("warnings", []))
        
        return {
            "errors_reduced": before_errors - after_errors,
            "warnings_reduced": before_warnings - after_warnings,
            "overall_improvement": (before_errors + before_warnings) - (after_errors + after_warnings)
        }
    
    def _generate_preprocessing_recommendations(self, before: Dict[str, Any], after: Dict[str, Any]) -> List[str]:
        """生成预处理建议"""
        recommendations = []
        
        # 基于转换结果的建议
        if "data_cleaning" in self.feature_transformations:
            cleaning_stats = self.feature_transformations["data_cleaning"]
            if "删除重复行" in str(cleaning_stats.get("operations", [])):
                recommendations.append("✅ 成功删除重复数据，数据一致性提升")
        
        if "missing_value_handling" in self.feature_transformations:
            missing_stats = self.feature_transformations["missing_value_handling"]
            if missing_stats.get("improvement", 0) > 0:
                recommendations.append("✅ 缺失值处理效果良好，数据完整性提升")
        
        if "outlier_handling" in self.feature_transformations:
            outlier_stats = self.feature_transformations["outlier_handling"]
            if outlier_stats.get("outliers_detected"):
                recommendations.append("✅ 异常值检测和处理完成，数据稳定性提升")
        
        # 基于质量对比的建议
        improvement = self._calculate_quality_improvement(before, after)
        if improvement.get("overall_improvement", 0) > 0:
            recommendations.append("🎉 整体数据质量显著提升，可以安全进行训练")
        
        if not recommendations:
            recommendations.append("📊 数据预处理完成，建议进行最终质量检查")
        
        return recommendations
    
    def _save_preprocessing_report(self, report: Dict[str, Any]):
        """保存预处理报告"""
        try:
            report_dir = Path("preprocessing_reports")
            report_dir.mkdir(exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_file = report_dir / f"preprocessing_report_{timestamp}.json"
            
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False, default=str)
            
            self.logger.info(f"📊 预处理报告已保存：{report_file}")
            
        except Exception as e:
            self.logger.warning(f"⚠️  保存预处理报告失败：{e}")