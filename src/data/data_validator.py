"""
数据验证器 - 解决数据质量检查缺失的问题
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
import logging
from pathlib import Path

class DataQualityValidator:
    """数据质量验证器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.validation_report = {}
    
    def validate_dataframe(self, df: pd.DataFrame, 
                          feature_config: Dict[str, Any],
                          dataset_name: str = "unknown") -> Dict[str, Any]:
        """
        验证DataFrame的数据质量
        
        Args:
            df: 待验证的DataFrame
            feature_config: 特征配置字典
            dataset_name: 数据集名称
            
        Returns:
            验证报告字典
        """
        report = {
            "dataset_name": dataset_name,
            "total_samples": len(df),
            "feature_issues": {},
            "warnings": [],
            "errors": []
        }
        
        # 检查必需特征是否存在
        required_features = set(feature_config.keys())
        available_features = set(df.columns)
        missing_features = required_features - available_features
        
        if missing_features:
            report["errors"].append(f"Missing required features: {missing_features}")
        
        # 检查每个特征的数据质量
        for feature_name, config in feature_config.items():
            if feature_name not in df.columns:
                continue
                
            feature_report = self._validate_feature(df[feature_name], config, feature_name)
            if feature_report:
                report["feature_issues"][feature_name] = feature_report
        
        # 检查数据分布异常
        report["distribution_warnings"] = self._check_distribution_anomalies(df)
        
        self.validation_report[dataset_name] = report
        return report
    
    def _validate_feature(self, series: pd.Series, config: Dict[str, Any], feature_name: str) -> Dict[str, Any]:
        """验证单个特征"""
        issues = {}
        
        # 检查缺失值
        missing_rate = series.isnull().sum() / len(series)
        if missing_rate > 0.1:  # 超过10%缺失
            issues["high_missing_rate"] = missing_rate
        
        # 根据特征类型进行专门检查
        feature_type = config.get("type", "unknown")
        
        if feature_type == "StringLookup":
            # 检查类别特征
            unique_values = series.nunique()
            if unique_values > 10000:  # 过多类别
                issues["too_many_categories"] = unique_values
        
        elif feature_type in ["Bucket", "Dense"]:
            # 检查数值特征
            if series.dtype not in ['int64', 'float64']:
                issues["wrong_dtype"] = str(series.dtype)
            
            # 检查异常值
            if series.notna().sum() > 0:
                q1, q3 = series.quantile([0.25, 0.75])
                iqr = q3 - q1
                outliers = ((series < (q1 - 1.5 * iqr)) | (series > (q3 + 1.5 * iqr))).sum()
                outlier_rate = outliers / len(series)
                if outlier_rate > 0.05:  # 超过5%异常值
                    issues["high_outlier_rate"] = outlier_rate
        
        return issues
    
    def _check_distribution_anomalies(self, df: pd.DataFrame) -> List[str]:
        """检查数据分布异常"""
        warnings = []
        
        # 检查样本不平衡（如果有标签列）
        label_columns = [col for col in df.columns if 'label' in col.lower()]
        for label_col in label_columns:
            if label_col in df.columns:
                value_counts = df[label_col].value_counts()
                if len(value_counts) > 1:
                    max_ratio = value_counts.max() / value_counts.sum()
                    if max_ratio > 0.95:  # 超过95%样本为同一类别
                        warnings.append(f"Severe class imbalance in {label_col}: {max_ratio:.3f}")
        
        return warnings
    
    def generate_report(self, output_path: Optional[str] = None) -> str:
        """生成验证报告"""
        report_lines = ["# Data Quality Validation Report\n"]
        
        for dataset_name, report in self.validation_report.items():
            report_lines.append(f"## Dataset: {dataset_name}")
            report_lines.append(f"- Total samples: {report['total_samples']}")
            
            if report["errors"]:
                report_lines.append("### Errors:")
                for error in report["errors"]:
                    report_lines.append(f"- {error}")
            
            if report["feature_issues"]:
                report_lines.append("### Feature Issues:")
                for feature, issues in report["feature_issues"].items():
                    report_lines.append(f"- {feature}: {issues}")
            
            if report["distribution_warnings"]:
                report_lines.append("### Distribution Warnings:")
                for warning in report["distribution_warnings"]:
                    report_lines.append(f"- {warning}")
        
        report_text = "\n".join(report_lines)
        
        if output_path:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(report_text)
        
        return report_text

class FlexibleDataLoader:
    """
    灵活的数据加载器 - 解决硬编码和格式假设过于严格的问题
    """
    
    def __init__(self, supported_formats: List[str] = None):
        self.supported_formats = supported_formats or ['parquet', 'csv', 'json']
        self.logger = logging.getLogger(__name__)
    
    def load_data(self, data_path: str, 
                  date_range: Optional[Tuple[str, str]] = None,
                  format_hint: Optional[str] = None) -> pd.DataFrame:
        """
        灵活加载数据，支持多种格式和日期筛选
        
        Args:
            data_path: 数据路径（文件或目录）
            date_range: 日期范围元组 (start_date, end_date)
            format_hint: 格式提示
            
        Returns:
            加载的DataFrame
        """
        path = Path(data_path)
        
        if path.is_file():
            return self._load_single_file(path, format_hint)
        elif path.is_dir():
            return self._load_directory(path, date_range, format_hint)
        else:
            raise FileNotFoundError(f"Path not found: {data_path}")
    
    def _load_single_file(self, file_path: Path, format_hint: Optional[str] = None) -> pd.DataFrame:
        """加载单个文件"""
        # 推断格式
        format_type = format_hint or file_path.suffix.lower().replace('.', '')
        
        if format_type == 'parquet':
            return pd.read_parquet(file_path)
        elif format_type == 'csv':
            return pd.read_csv(file_path)
        elif format_type == 'json':
            return pd.read_json(file_path)
        else:
            raise ValueError(f"Unsupported format: {format_type}")
    
    def _load_directory(self, dir_path: Path, 
                       date_range: Optional[Tuple[str, str]] = None,
                       format_hint: Optional[str] = None) -> pd.DataFrame:
        """加载目录中的数据文件"""
        dfs = []
        
        # 如果有日期筛选，则按日期目录过滤
        if date_range:
            start_date, end_date = date_range
            date_dirs = [d for d in dir_path.iterdir() 
                        if d.is_dir() and d.name.startswith('datetime=')]
            
            filtered_dirs = []
            for date_dir in date_dirs:
                date_str = date_dir.name.replace('datetime=', '')
                if start_date <= date_str <= end_date:
                    filtered_dirs.append(date_dir)
            
            # 加载过滤后的目录
            for date_dir in filtered_dirs:
                for file_path in date_dir.rglob('*.parquet'):
                    df = self._load_single_file(file_path)
                    df['source_date'] = date_str  # 添加日期标识
                    dfs.append(df)
        else:
            # 加载所有文件
            for file_path in dir_path.rglob('*'):
                if file_path.is_file() and file_path.suffix in ['.parquet', '.csv', '.json']:
                    try:
                        df = self._load_single_file(file_path)
                        dfs.append(df)
                    except Exception as e:
                        self.logger.warning(f"Failed to load {file_path}: {e}")
        
        if not dfs:
            raise ValueError(f"No data files found in {dir_path}")
        
        return pd.concat(dfs, ignore_index=True)