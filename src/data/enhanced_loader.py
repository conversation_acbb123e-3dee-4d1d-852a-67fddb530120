"""
增强数据加载器 - 集成数据质量检查

主要改进：
1. 自动数据质量验证
2. 数据统计报告生成
3. 异常数据检测和处理
4. 更灵活的数据格式支持
"""

import pandas as pd
import numpy as np
import logging
import json
import yaml
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple, Union
import warnings
from datetime import datetime

from src.data.data_validator import DataQualityValidator, FlexibleDataLoader

class EnhancedDataLoader:
    """
    增强版数据加载器
    
    功能特点：
    - 自动数据质量检查
    - 支持多种数据格式
    - 生成数据质量报告
    - 异常数据处理建议
    """
    
    def __init__(self, 
                 dataset_config: Union[str, Dict[str, Any]],
                 enable_validation: bool = True,
                 validation_level: str = "standard"):
        """
        初始化增强数据加载器
        
        Args:
            dataset_config: 数据集配置（文件路径或配置字典）
            enable_validation: 是否启用数据验证
            validation_level: 验证级别 ("strict", "standard", "minimal")
        """
        self.logger = logging.getLogger(__name__)
        self.enable_validation = enable_validation
        self.validation_level = validation_level
        
        # 加载配置
        self.config = self._load_config(dataset_config)
        
        # 初始化组件
        self.flexible_loader = FlexibleDataLoader()
        if enable_validation:
            self.validator = DataQualityValidator()
        
        # 数据统计
        self.load_statistics = {}
        self.validation_reports = {}
        
        self.logger.info(f"Enhanced data loader initialized with validation: {enable_validation}")
    
    def _load_config(self, config_input: Union[str, Dict[str, Any]]) -> Dict[str, Any]:
        """加载数据集配置"""
        if isinstance(config_input, str):
            config_path = Path(config_input)
            if config_path.suffix.lower() == '.yaml':
                with open(config_path, 'r', encoding='utf-8') as f:
                    return yaml.safe_load(f)
            elif config_path.suffix.lower() == '.json':
                with open(config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                raise ValueError(f"Unsupported config format: {config_path.suffix}")
        elif isinstance(config_input, dict):
            return config_input
        else:
            raise ValueError("Config must be file path or dictionary")
    
    def load_dataset(self, 
                    dates: List[str],
                    data_root: Optional[str] = None,
                    columns: Optional[List[str]] = None,
                    extra_datasets: Optional[List[str]] = None,
                    apply_preprocessing: bool = True,
                    generate_report: bool = True) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """
        加载数据集并进行质量检查
        
        Args:
            dates: 日期列表
            data_root: 数据根目录
            columns: 需要的列
            extra_datasets: 额外数据集
            apply_preprocessing: 是否应用预处理
            generate_report: 是否生成报告
            
        Returns:
            Tuple[DataFrame, 质量报告]
        """
        start_time = datetime.now()
        self.logger.info(f"🔄 开始加载数据集：dates={dates}")
        
        try:
            # 1. 确定数据路径
            data_root = data_root or self.config.get("data_root", "data")
            
            # 2. 加载主数据集
            main_df = self._load_main_dataset(dates, data_root, columns)
            
            # 3. 加载额外数据集
            if extra_datasets:
                extra_df = self._load_extra_datasets(extra_datasets, data_root)
                main_df = self._merge_datasets(main_df, extra_df)
            
            # 4. 应用预处理
            if apply_preprocessing:
                main_df = self._apply_preprocessing(main_df)
            
            # 5. 数据质量验证
            quality_report = {}
            if self.enable_validation:
                quality_report = self._validate_data_quality(main_df)
            
            # 6. 生成加载统计
            load_time = (datetime.now() - start_time).total_seconds()
            load_stats = self._generate_load_statistics(main_df, load_time, dates)
            
            # 7. 生成综合报告
            if generate_report:
                comprehensive_report = self._generate_comprehensive_report(
                    load_stats, quality_report, main_df)
                self._save_report(comprehensive_report, dates)
            
            self.logger.info(f"✅ 数据加载完成：{len(main_df)} 行，{len(main_df.columns)} 列")
            
            # 返回数据和报告
            final_report = {
                "load_statistics": load_stats,
                "quality_report": quality_report,
                "recommendations": self._generate_recommendations(quality_report)
            }
            
            return main_df, final_report
            
        except Exception as e:
            self.logger.error(f"❌ 数据加载失败：{e}")
            raise
    
    def _load_main_dataset(self, dates: List[str], data_root: str, 
                          columns: Optional[List[str]] = None) -> pd.DataFrame:
        """加载主数据集"""
        dfs = []
        
        for date in dates:
            self.logger.info(f"📁 加载日期分区：{date}")
            
            # 构建数据路径
            date_path = Path(data_root) / f"datetime={date}"
            
            if not date_path.exists():
                self.logger.warning(f"⚠️  日期分区不存在：{date_path}")
                continue
            
            try:
                # 使用灵活加载器加载
                date_df = self.flexible_loader.load_data(str(date_path))
                
                # 添加日期标识
                date_df['_source_date'] = date
                
                # 列过滤
                if columns:
                    available_columns = [col for col in columns if col in date_df.columns]
                    missing_columns = set(columns) - set(available_columns)
                    if missing_columns:
                        self.logger.warning(f"缺失列：{missing_columns}")
                    date_df = date_df[available_columns + ['_source_date']]
                
                dfs.append(date_df)
                self.logger.info(f"   ✓ 加载完成：{len(date_df)} 行")
                
            except Exception as e:
                self.logger.error(f"❌ 加载日期 {date} 失败：{e}")
                continue
        
        if not dfs:
            raise ValueError("没有成功加载任何数据分区")
        
        # 合并所有分区
        combined_df = pd.concat(dfs, ignore_index=True)
        self.logger.info(f"📊 合并完成：{len(combined_df)} 行来自 {len(dfs)} 个分区")
        
        return combined_df
    
    def _load_extra_datasets(self, extra_datasets: List[str], data_root: str) -> pd.DataFrame:
        """加载额外数据集"""
        extra_dfs = []
        
        for dataset_path in extra_datasets:
            try:
                self.logger.info(f"📁 加载额外数据集：{dataset_path}")
                
                full_path = Path(data_root) / dataset_path
                extra_df = self.flexible_loader.load_data(str(full_path))
                extra_df['_source_extra'] = dataset_path
                extra_dfs.append(extra_df)
                
                self.logger.info(f"   ✓ 额外数据集加载完成：{len(extra_df)} 行")
                
            except Exception as e:
                self.logger.error(f"❌ 加载额外数据集 {dataset_path} 失败：{e}")
                continue
        
        if extra_dfs:
            return pd.concat(extra_dfs, ignore_index=True)
        else:
            return pd.DataFrame()
    
    def _merge_datasets(self, main_df: pd.DataFrame, extra_df: pd.DataFrame) -> pd.DataFrame:
        """合并数据集"""
        if extra_df.empty:
            return main_df
        
        # 简单合并（实际项目中可能需要更复杂的合并逻辑）
        self.logger.info(f"🔗 合并数据集：主数据 {len(main_df)} 行 + 额外数据 {len(extra_df)} 行")
        
        # 这里可以实现更复杂的合并逻辑，如按用户ID合并等
        combined_df = pd.concat([main_df, extra_df], ignore_index=True, sort=False)
        
        self.logger.info(f"   ✓ 合并完成：{len(combined_df)} 行")
        return combined_df
    
    def _apply_preprocessing(self, df: pd.DataFrame) -> pd.DataFrame:
        """应用预处理"""
        preprocessing_config = self.config.get("preprocessing", {})
        
        if not preprocessing_config:
            self.logger.info("⚠️  没有预处理配置，跳过预处理")
            return df
        
        self.logger.info("🔧 开始应用预处理...")
        
        processed_df = df.copy()
        
        # 1. 删除重复行
        if preprocessing_config.get("remove_duplicates", True):
            before_count = len(processed_df)
            processed_df = processed_df.drop_duplicates()
            after_count = len(processed_df)
            if before_count != after_count:
                self.logger.info(f"   - 删除重复行：{before_count - after_count} 行")
        
        # 2. 处理缺失值
        missing_strategy = preprocessing_config.get("missing_value_strategy", "fill_default")
        if missing_strategy == "fill_default":
            categorical_token = preprocessing_config.get("categorical_missing_token", "<UNK>")
            numerical_value = preprocessing_config.get("numerical_missing_value", 0)
            
            for column in processed_df.columns:
                if processed_df[column].dtype == 'object':
                    processed_df[column] = processed_df[column].fillna(categorical_token)
                else:
                    processed_df[column] = processed_df[column].fillna(numerical_value)
            
            self.logger.info(f"   - 填充缺失值：类别特征用'{categorical_token}'，数值特征用{numerical_value}")
        
        # 3. 异常值检测
        if preprocessing_config.get("outlier_detection", True):
            outlier_threshold = preprocessing_config.get("outlier_threshold", 3.0)
            outlier_count = self._detect_and_handle_outliers(processed_df, outlier_threshold)
            if outlier_count > 0:
                self.logger.info(f"   - 检测到异常值：{outlier_count} 个")
        
        self.logger.info("✅ 预处理完成")
        return processed_df
    
    def _detect_and_handle_outliers(self, df: pd.DataFrame, threshold: float = 3.0) -> int:
        """检测和处理异常值"""
        outlier_count = 0
        
        numerical_columns = df.select_dtypes(include=[np.number]).columns
        
        for column in numerical_columns:
            if column.startswith('_'):  # 跳过内部列
                continue
            
            z_scores = np.abs((df[column] - df[column].mean()) / df[column].std())
            outliers = z_scores > threshold
            
            outlier_count += outliers.sum()
            
            if outliers.any():
                # 简单处理：用中位数替换异常值
                median_value = df[column].median()
                df.loc[outliers, column] = median_value
        
        return outlier_count
    
    def _validate_data_quality(self, df: pd.DataFrame) -> Dict[str, Any]:
        """验证数据质量"""
        if not self.enable_validation:
            return {}
        
        self.logger.info("🔍 开始数据质量验证...")
        
        # 构建特征配置（从config中提取或使用默认）
        feature_config = self._build_feature_config_for_validation(df)
        
        # 执行验证
        validation_report = self.validator.validate_dataframe(
            df, feature_config, f"dataset_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        
        # 根据验证级别处理结果
        self._process_validation_results(validation_report)
        
        self.logger.info("✅ 数据质量验证完成")
        return validation_report
    
    def _build_feature_config_for_validation(self, df: pd.DataFrame) -> Dict[str, Any]:
        """为验证构建特征配置"""
        feature_config = {}
        
        for column in df.columns:
            if column.startswith('_'):  # 跳过内部列
                continue
            
            if df[column].dtype == 'object':
                feature_config[column] = {"type": "StringLookup"}
            elif df[column].dtype in ['int64', 'float64']:
                feature_config[column] = {"type": "Bucket"}
            else:
                feature_config[column] = {"type": "Unknown"}
        
        return feature_config
    
    def _process_validation_results(self, validation_report: Dict[str, Any]):
        """处理验证结果"""
        errors = validation_report.get("errors", [])
        warnings = validation_report.get("warnings", [])
        
        # 根据验证级别决定如何处理
        if self.validation_level == "strict":
            if errors:
                raise ValueError(f"严格验证模式下发现错误：{errors}")
            if warnings:
                self.logger.warning(f"验证警告：{warnings}")
        
        elif self.validation_level == "standard":
            if errors:
                self.logger.error(f"验证错误（继续执行）：{errors}")
            if warnings:
                self.logger.warning(f"验证警告：{warnings}")
        
        elif self.validation_level == "minimal":
            # 只记录，不采取行动
            if errors or warnings:
                self.logger.info(f"验证发现问题：错误 {len(errors)} 个，警告 {len(warnings)} 个")
    
    def _generate_load_statistics(self, df: pd.DataFrame, load_time: float, 
                                dates: List[str]) -> Dict[str, Any]:
        """生成加载统计"""
        stats = {
            "load_time_seconds": load_time,
            "total_rows": len(df),
            "total_columns": len(df.columns),
            "dates_loaded": dates,
            "memory_usage_mb": df.memory_usage(deep=True).sum() / 1024 / 1024,
            "column_types": df.dtypes.value_counts().to_dict(),
            "missing_value_summary": df.isnull().sum().to_dict(),
            "load_timestamp": datetime.now().isoformat()
        }
        
        self.load_statistics = stats
        return stats
    
    def _generate_comprehensive_report(self, load_stats: Dict[str, Any],
                                     quality_report: Dict[str, Any],
                                     df: pd.DataFrame) -> Dict[str, Any]:
        """生成综合报告"""
        report = {
            "summary": {
                "dataset_size": f"{load_stats['total_rows']} 行 × {load_stats['total_columns']} 列",
                "load_time": f"{load_stats['load_time_seconds']:.2f} 秒",
                "memory_usage": f"{load_stats['memory_usage_mb']:.2f} MB",
                "data_quality_score": self._calculate_quality_score(quality_report)
            },
            "load_statistics": load_stats,
            "quality_report": quality_report,
            "data_profile": {
                "numerical_columns": len(df.select_dtypes(include=[np.number]).columns),
                "categorical_columns": len(df.select_dtypes(include=['object']).columns),
                "missing_value_rate": df.isnull().sum().sum() / (len(df) * len(df.columns)),
                "unique_values_per_column": {col: df[col].nunique() for col in df.columns if not col.startswith('_')}
            }
        }
        
        return report
    
    def _calculate_quality_score(self, quality_report: Dict[str, Any]) -> float:
        """计算数据质量分数"""
        if not quality_report:
            return 1.0  # 没有验证时默认满分
        
        errors = len(quality_report.get("errors", []))
        warnings = len(quality_report.get("warnings", []))
        feature_issues = len(quality_report.get("feature_issues", {}))
        
        # 简单的评分逻辑
        penalty = errors * 0.3 + warnings * 0.1 + feature_issues * 0.05
        score = max(0.0, 1.0 - penalty)
        
        return round(score, 3)
    
    def _generate_recommendations(self, quality_report: Dict[str, Any]) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        if not quality_report:
            return recommendations
        
        errors = quality_report.get("errors", [])
        feature_issues = quality_report.get("feature_issues", {})
        
        if errors:
            recommendations.append("🔴 发现数据错误，建议检查数据源和ETL流程")
        
        for feature_name, issues in feature_issues.items():
            if "high_missing_rate" in issues:
                recommendations.append(f"⚠️  特征 {feature_name} 缺失率过高，建议检查数据收集流程")
            
            if "too_many_categories" in issues:
                recommendations.append(f"⚠️  特征 {feature_name} 类别过多，建议进行特征工程")
            
            if "high_outlier_rate" in issues:
                recommendations.append(f"⚠️  特征 {feature_name} 异常值过多，建议检查数据质量")
        
        if not recommendations:
            recommendations.append("✅ 数据质量良好，可以安全进行训练")
        
        return recommendations
    
    def _save_report(self, report: Dict[str, Any], dates: List[str]):
        """保存报告"""
        try:
            report_dir = Path("data_quality_reports")
            report_dir.mkdir(exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            dates_str = "_".join(dates)
            report_file = report_dir / f"data_quality_{dates_str}_{timestamp}.json"
            
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False, default=str)
            
            self.logger.info(f"📊 数据质量报告已保存：{report_file}")
            
        except Exception as e:
            self.logger.warning(f"⚠️  保存报告失败：{e}")
    
    def get_data_summary(self) -> str:
        """获取数据摘要文本"""
        if not self.load_statistics:
            return "没有可用的数据统计信息"
        
        stats = self.load_statistics
        
        summary = f"""
📊 数据加载摘要
===============
• 数据规模：{stats['total_rows']:,} 行 × {stats['total_columns']} 列
• 加载时间：{stats['load_time_seconds']:.2f} 秒
• 内存使用：{stats['memory_usage_mb']:.2f} MB
• 加载日期：{', '.join(stats['dates_loaded'])}
• 数据类型：{stats['column_types']}
• 加载时间：{stats['load_timestamp']}
"""
        return summary