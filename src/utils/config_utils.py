"""
Configuration utilities for conversion rate prediction models.
"""
import json
import logging
import os
from pathlib import Path


class ConfigManager:
    """Configuration manager for model training and evaluation."""
    
    def __init__(self):
        """Initialize configuration manager."""
        self.logger = logging.getLogger(__name__)
    
    @staticmethod
    def load_config(config_path):
        """
        Load configuration from JSON file.
        
        Args:
            config_path (str): Path to configuration file.
            
        Returns:
            dict: Configuration dictionary.
        """
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"Configuration file not found: {config_path}")
            
        with open(config_path, 'r') as f:
            config = json.load(f)
            
        return config
    
    @staticmethod
    def save_config(config, output_path):
        """
        Save configuration to JSON file.
        
        Args:
            config (dict): Configuration dictionary.
            output_path (str): Path to save configuration.
            
        Returns:
            bool: Whether saving was successful.
        """
        try:
            # Ensure directory exists
            Path(output_path).parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=True, indent=4)
                
            return True
        except Exception as e:
            logging.error(f"Failed to save configuration: {e}")
            return False
    
    @staticmethod
    def merge_configs(base_config, override_config):
        """
        Merge two configuration dictionaries.
        
        Args:
            base_config (dict): Base configuration.
            override_config (dict): Override configuration.
            
        Returns:
            dict: Merged configuration.
        """
        # Deep copy to avoid modifying original
        result = base_config.copy()
        
        # Recursively update dictionary
        for key, value in override_config.items():
            if isinstance(value, dict) and key in result and isinstance(result[key], dict):
                # Recursive merge for nested dictionaries
                result[key] = ConfigManager.merge_configs(result[key], value)
            else:
                # Direct update for non-dictionary values
                result[key] = value
                
        return result
    
    @staticmethod
    def parse_arguments(args):
        """
        Parse command line arguments into configuration.
        
        Args:
            args (argparse.Namespace): Command line arguments.
            
        Returns:
            dict: Configuration dictionary.
        """
        config = {}
        
        # Convert all arguments to dictionary
        for key, value in vars(args).items():
            if value is not None:  # Only include non-None values
                config[key] = value
                
        return config
    
    def validate_model_config(self, config):
        """
        Validate model configuration.
        
        Args:
            config (dict): Model configuration.
            
        Returns:
            tuple: (is_valid, errors)
        """
        errors = []
        
        # Check required fields
        required_fields = ["network_name", "RawFeature"]
        for field in required_fields:
            if field not in config:
                errors.append(f"Missing required field: {field}")
                
        # Validate network name
        network_name = config.get("network_name")
        if network_name:
            network_file = f"src/models/networks/{network_name}.py"
            if not os.path.exists(network_file):
                errors.append(f"Network implementation not found: {network_file}")
                
        # Validate raw features
        raw_features = config.get("RawFeature", {})
        if not raw_features:
            errors.append("No raw features defined")
            
        # Log all validation errors
        if errors:
            for error in errors:
                self.logger.error(f"Configuration error: {error}")
                
        return len(errors) == 0, errors 