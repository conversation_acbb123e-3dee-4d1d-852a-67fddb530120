answer_sales_call_duration_s_180d_cnt:
  bin_boundarie:
  - 0.5
  - 9.5
  - 11.5
  - 14.5
  - 17.5
  - 20.5
  - 23.5
  - 28.5
  - 33.5
  - 42.5
  - 55.5
  - 70.5
  - 113.5
  - 180.5
  - 303.5
  dtype: Bucket
answer_sales_call_duration_s_1d_cnt:
  bin_boundarie:
  - 0.5
  dtype: Bucket
answer_sales_call_duration_s_30d_cnt:
  bin_boundarie:
  - 0.5
  - 9.5
  - 11.5
  - 14.5
  - 18.5
  - 23.5
  - 28.5
  - 33.5
  - 40.5
  - 49.5
  - 66.5
  - 84.5
  - 113.5
  - 156.5
  - 256.5
  dtype: <PERSON>et
answer_sales_call_duration_s_60d_cnt:
  bin_boundarie:
  - 0.5
  - 9.5
  - 12.5
  - 14.5
  - 17.5
  - 21.5
  - 24.5
  - 30.5
  - 40.5
  - 51.5
  - 66.5
  - 108.5
  - 149.5
  - 256.5
  dtype: Bucket
answer_sales_call_duration_s_7d_cnt:
  bin_boundarie:
  - 0.5
  - 8.5
  - 11.5
  - 14.5
  - 18.5
  - 23.5
  - 26.5
  - 30.5
  - 36.5
  - 45.5
  - 60.5
  - 74.5
  - 114.5
  - 213.5
  dtype: <PERSON><PERSON>
answer_sales_call_duration_s_90d_cnt:
  bin_boundarie:
  - 0.5
  - 9.5
  - 11.5
  - 14.5
  - 17.5
  - 20.5
  - 23.5
  - 30.5
  - 42.5
  - 54.5
  - 70.5
  - 113.5
  - 177.5
  - 298.5
  dtype: Bucket
answer_sales_call_duration_s_last:
  bin_boundarie:
  - 0.5
  - 7.5
  - 9.5
  - 11.5
  - 14.5
  - 16.5
  - 18.5
  - 20.5
  - 23.5
  - 29.5
  - 40.5
  - 50.5
  - 79.5
  - 129.5
  dtype: Bucket
fellow_follow_180d_DSLA:
  bin_boundarie:
  - 0.5
  - 2.5
  - 4.5
  - 6.5
  - 9.5
  - 14.5
  - 21.5
  - 29.5
  - 38.5
  - 49.5
  - 68.5
  - 83.5
  - 107.5
  - 127.5
  - 179.5
  dtype: Bucket
fellow_follow_180d_cnt:
  bin_boundarie:
  - 0.5
  - 1.5
  - 2.5
  - 3.5
  - 4.5
  - 5.5
  - 6.5
  - 8.5
  - 10.5
  - 13.5
  - 18.5
  - 26.5
  dtype: Bucket
fellow_follow_1d_cnt:
  bin_boundarie:
  - 0.5
  - 1.5
  dtype: Bucket
fellow_follow_30d_cnt:
  bin_boundarie:
  - 0.5
  - 1.5
  - 2.5
  - 3.5
  - 4.5
  - 6.5
  - 9.5
  dtype: Bucket
fellow_follow_60d_cnt:
  bin_boundarie:
  - 0.5
  - 1.5
  - 2.5
  - 3.5
  - 4.5
  - 6.5
  - 10.5
  - 14.5
  dtype: Bucket
fellow_follow_7d_cnt:
  bin_boundarie:
  - 0.5
  - 1.5
  - 2.5
  - 3.5
  dtype: Bucket
fellow_follow_90d_cnt:
  bin_boundarie:
  - 0.5
  - 1.5
  - 2.5
  - 3.5
  - 4.5
  - 5.5
  - 7.5
  - 10.5
  - 15.5
  dtype: Bucket
universe_action_cnt_14d:
  bin_boundarie:
  - 0.5
  - 1.5
  - 2.5
  - 4.5
  - 8.5
  - 12.5
  - 20.5
  - 28.5
  - 37.5
  - 55.5
  - 77.5
  - 114.5
  dtype: Bucket
universe_action_cnt_180d:
  bin_boundarie:
  - 0.5
  - 1.5
  - 2.5
  - 3.5
  - 6.5
  - 11.5
  - 23.5
  - 41.5
  - 226.5
  dtype: Bucket
universe_action_cnt_1d:
  bin_boundarie:
  - 0.5
  - 1.5
  - 2.5
  - 3.5
  - 4.5
  - 5.5
  - 6.5
  - 7.5
  - 9.5
  - 13.5
  dtype: Bucket
universe_action_cnt_30d:
  bin_boundarie:
  - 0.5
  - 1.5
  - 2.5
  - 3.5
  - 6.5
  - 11.5
  - 19.5
  - 32.5
  - 50.5
  - 74.5
  - 125.5
  - 206.5
  dtype: Bucket
universe_action_cnt_60d:
  bin_boundarie:
  - 0.5
  - 1.5
  - 2.5
  - 3.5
  - 6.5
  - 9.5
  - 13.5
  - 23.5
  - 63.5
  - 116.5
  - 243.5
  dtype: Bucket
universe_action_cnt_7d:
  bin_boundarie:
  - 0.5
  - 1.5
  - 2.5
  - 4.5
  - 6.5
  - 9.5
  - 13.5
  - 16.5
  - 21.5
  - 28.5
  - 40.5
  - 59.5
  dtype: Bucket
universe_action_cnt_90d:
  bin_boundarie:
  - 0.5
  - 1.5
  - 2.5
  - 3.5
  - 5.5
  - 9.5
  - 19.5
  - 34.5
  - 69.5
  - 155.5
  - 339.5
  dtype: Bucket
user_car_core_action_cnt_14d:
  bin_boundarie:
  - 0.5
  - 1.5
  - 2.5
  - 3.5
  - 4.5
  - 5.5
  - 8.5
  - 12.5
  - 18.5
  dtype: Bucket
user_car_core_action_cnt_180d:
  bin_boundarie:
  - 0.5
  - 1.5
  - 2.5
  - 3.5
  - 4.5
  - 6.5
  - 8.5
  - 11.5
  - 16.5
  - 34.5
  dtype: Bucket
user_car_core_action_cnt_1d:
  bin_boundarie:
  - 0.5
  - 1.5
  - 2.5
  - 3.5
  - 4.5
  - 5.5
  - 7.5
  dtype: Bucket
user_car_core_action_cnt_30d:
  bin_boundarie:
  - 0.5
  - 1.5
  - 2.5
  - 3.5
  - 4.5
  - 5.5
  - 7.5
  - 9.5
  - 15.5
  - 23.5
  dtype: Bucket
user_car_core_action_cnt_60d:
  bin_boundarie:
  - 0.5
  - 1.5
  - 2.5
  - 3.5
  - 4.5
  - 5.5
  - 9.5
  - 13.5
  - 20.5
  dtype: Bucket
user_car_core_action_cnt_7d:
  bin_boundarie:
  - 0.5
  - 1.5
  - 2.5
  - 3.5
  - 4.5
  - 5.5
  - 8.5
  - 11.5
  - 16.5
  dtype: Bucket
user_car_core_action_cnt_90d:
  bin_boundarie:
  - 0.5
  - 1.5
  - 2.5
  - 3.5
  - 4.5
  - 5.5
  - 9.5
  - 15.5
  - 23.5
  dtype: Bucket
user_core_action_cnt_14d:
  bin_boundarie:
  - 0.5
  - 1.5
  - 2.5
  - 3.5
  - 4.5
  - 6.5
  - 11.5
  dtype: Bucket
user_core_action_cnt_180d:
  bin_boundarie:
  - 0.5
  - 1.5
  - 2.5
  - 3.5
  - 4.5
  - 5.5
  - 7.5
  - 11.5
  - 17.5
  dtype: Bucket
user_core_action_cnt_1d:
  bin_boundarie:
  - 0.5
  - 1.5
  - 2.5
  - 3.5
  - 4.5
  dtype: Bucket
user_core_action_cnt_30d:
  bin_boundarie:
  - 0.5
  - 1.5
  - 2.5
  - 3.5
  - 4.5
  - 6.5
  - 9.5
  - 14.5
  dtype: Bucket
user_core_action_cnt_60d:
  bin_boundarie:
  - 0.5
  - 1.5
  - 2.5
  - 3.5
  - 4.5
  - 6.5
  - 9.5
  - 15.5
  dtype: Bucket
user_core_action_cnt_7d:
  bin_boundarie:
  - 0.5
  - 1.5
  - 2.5
  - 3.5
  - 4.5
  - 6.5
  dtype: Bucket
user_core_action_cnt_90d:
  bin_boundarie:
  - 0.5
  - 1.5
  - 2.5
  - 3.5
  - 4.5
  - 5.5
  - 7.5
  - 11.5
  - 18.5
  dtype: Bucket
user_core_answer_sales_pc_nio_180d_cnt:
  bin_boundarie:
  - 0.5
  - 1.5
  - 2.5
  - 4.5
  dtype: Bucket
user_core_answer_sales_pc_nio_1d_cnt:
  bin_boundarie:
  - 0.5
  dtype: Bucket
user_core_answer_sales_pc_nio_30d_cnt:
  bin_boundarie:
  - 0.5
  - 1.5
  - 2.5
  dtype: Bucket
user_core_answer_sales_pc_nio_60d_cnt:
  bin_boundarie:
  - 0.5
  - 1.5
  - 2.5
  - 3.5
  dtype: Bucket
user_core_answer_sales_pc_nio_7d_cnt:
  bin_boundarie:
  - 0.5
  - 1.5
  dtype: Bucket
user_core_answer_sales_pc_nio_90d_cnt:
  bin_boundarie:
  - 0.5
  - 1.5
  - 2.5
  - 3.5
  dtype: Bucket
user_core_book_td_nio_180d_cnt:
  bin_boundarie:
  - 0.5
  - 1.5
  - 2.5
  dtype: Bucket
user_core_book_td_nio_1d_cnt:
  bin_boundarie:
  - 0.5
  - 1.5
  dtype: Bucket
user_core_book_td_nio_30d_cnt:
  bin_boundarie:
  - 0.5
  - 1.5
  dtype: Bucket
user_core_book_td_nio_60d_cnt:
  bin_boundarie:
  - 0.5
  - 1.5
  dtype: Bucket
user_core_book_td_nio_7d_cnt:
  bin_boundarie:
  - 0.5
  - 1.5
  dtype: Bucket
user_core_book_td_nio_90d_cnt:
  bin_boundarie:
  - 0.5
  - 1.5
  dtype: Bucket
user_core_book_td_nio_DSLA:
  bin_boundarie:
  - 3.5
  - 9.5
  - 17.5
  - 32.5
  - 52.5
  - 70.5
  - 86.5
  - 107.5
  - 121.5
  - 130.5
  - 138.5
  - 147.5
  - 156.5
  - 166.5
  - 179.5
  dtype: Bucket
user_core_buy_cm_nioapp_180d_cnt:
  bin_boundarie:
  - 0.5
  - 1.5
  - 2.5
  - 3.5
  - 4.5
  - 5.5
  - 7.5
  dtype: Bucket
user_core_buy_cm_nioapp_1d_cnt:
  bin_boundarie:
  - 0.5
  - 1.5
  - 2.5
  dtype: Bucket
user_core_buy_cm_nioapp_30d_cnt:
  bin_boundarie:
  - 0.5
  - 1.5
  - 2.5
  - 3.5
  dtype: Bucket
user_core_buy_cm_nioapp_60d_cnt:
  bin_boundarie:
  - 0.5
  - 1.5
  - 2.5
  - 3.5
  - 5.5
  dtype: Bucket
user_core_buy_cm_nioapp_7d_cnt:
  bin_boundarie:
  - 0.5
  - 1.5
  - 2.5
  - 3.5
  dtype: Bucket
user_core_buy_cm_nioapp_90d_cnt:
  bin_boundarie:
  - 0.5
  - 1.5
  - 2.5
  - 3.5
  - 5.5
  dtype: Bucket
user_core_exp_td_nio_180d_cnt:
  bin_boundarie:
  - 0.5
  - 1.5
  - 2.5
  dtype: Bucket
user_core_exp_td_nio_1d_cnt:
  bin_boundarie:
  - 0.5
  dtype: Bucket
user_core_exp_td_nio_30d_cnt:
  bin_boundarie:
  - 0.5
  - 1.5
  dtype: Bucket
user_core_exp_td_nio_60d_cnt:
  bin_boundarie:
  - 0.5
  - 1.5
  dtype: Bucket
user_core_exp_td_nio_7d_cnt:
  bin_boundarie:
  - 0.5
  - 1.5
  dtype: Bucket
user_core_exp_td_nio_90d_cnt:
  bin_boundarie:
  - 0.5
  - 1.5
  dtype: Bucket
user_core_exp_td_nio_DSLA:
  bin_boundarie:
  - 5.5
  - 10.5
  - 16.5
  - 28.5
  - 36.5
  - 45.5
  - 59.5
  - 78.5
  - 90.5
  - 101.5
  - 115.5
  - 137.5
  - 154.5
  - 165.5
  - 179.5
  dtype: Bucket
user_core_fl_f2f_interview_nio_180d_cnt:
  bin_boundarie:
  - 0.5
  - 1.5
  - 2.5
  dtype: Bucket
user_core_fl_f2f_interview_nio_1d_cnt:
  bin_boundarie:
  - 0.5
  - 1.5
  dtype: Bucket
user_core_fl_f2f_interview_nio_30d_cnt:
  bin_boundarie:
  - 0.5
  - 1.5
  dtype: Bucket
user_core_fl_f2f_interview_nio_60d_cnt:
  bin_boundarie:
  - 0.5
  - 1.5
  - 2.5
  dtype: Bucket
user_core_fl_f2f_interview_nio_7d_cnt:
  bin_boundarie:
  - 0.5
  - 1.5
  dtype: Bucket
user_core_fl_f2f_interview_nio_90d_cnt:
  bin_boundarie:
  - 0.5
  - 1.5
  - 2.5
  dtype: Bucket
user_core_fl_offline_im_nio_180d_cnt:
  bin_boundarie:
  - 0.5
  - 1.5
  - 2.5
  - 3.5
  - 4.5
  - 5.5
  - 7.5
  - 11.5
  dtype: Bucket
user_core_fl_offline_im_nio_1d_cnt:
  bin_boundarie:
  - 0.5
  dtype: Bucket
user_core_fl_offline_im_nio_30d_cnt:
  bin_boundarie:
  - 0.5
  - 1.5
  - 2.5
  - 3.5
  - 4.5
  dtype: Bucket
user_core_fl_offline_im_nio_60d_cnt:
  bin_boundarie:
  - 0.5
  - 1.5
  - 2.5
  - 3.5
  - 5.5
  - 7.5
  dtype: Bucket
user_core_fl_offline_im_nio_7d_cnt:
  bin_boundarie:
  - 0.5
  - 1.5
  - 2.5
  dtype: Bucket
user_core_fl_offline_im_nio_90d_cnt:
  bin_boundarie:
  - 0.5
  - 1.5
  - 2.5
  - 3.5
  - 4.5
  - 8.5
  dtype: Bucket
user_core_onvo_user_identity:
  dtype: StringLookup
user_core_pay_ncar_dp_nio_1d_cnt:
  bin_boundarie:
  - 0.5
  dtype: Bucket
user_core_pay_ncar_intention_nio_180d_cnt:
  bin_boundarie:
  - 0.5
  - 1.5
  dtype: Bucket
user_core_pay_ncar_intention_nio_1d_cnt:
  bin_boundarie:
  - 0.5
  dtype: Bucket
user_core_pay_ncar_intention_nio_30d_cnt:
  bin_boundarie:
  - 0.5
  - 1.5
  dtype: Bucket
user_core_pay_ncar_intention_nio_60d_cnt:
  bin_boundarie:
  - 0.5
  - 1.5
  dtype: Bucket
user_core_pay_ncar_intention_nio_7d_cnt:
  bin_boundarie:
  - 0.5
  dtype: Bucket
user_core_pay_ncar_intention_nio_90d_cnt:
  bin_boundarie:
  - 0.5
  - 1.5
  dtype: Bucket
user_core_pay_ncar_intention_nio_DSLA:
  bin_boundarie:
  - 7.5
  - 13.5
  - 14.5
  - 97.5
  - 108.5
  - 128.5
  - 179.5
  dtype: Bucket
user_core_view_finance_calc_nioapp_180d_cnt:
  bin_boundarie:
  - 0.5
  - 1.5
  - 2.5
  - 3.5
  - 4.5
  - 8.5
  dtype: Bucket
user_core_view_finance_calc_nioapp_1d_cnt:
  bin_boundarie:
  - 0.5
  - 1.5
  - 2.5
  - 3.5
  dtype: Bucket
user_core_view_finance_calc_nioapp_30d_cnt:
  bin_boundarie:
  - 0.5
  - 1.5
  - 2.5
  - 3.5
  - 5.5
  dtype: Bucket
user_core_view_finance_calc_nioapp_60d_cnt:
  bin_boundarie:
  - 0.5
  - 1.5
  - 2.5
  - 3.5
  - 4.5
  - 7.5
  dtype: Bucket
user_core_view_finance_calc_nioapp_7d_cnt:
  bin_boundarie:
  - 0.5
  - 1.5
  - 2.5
  - 3.5
  dtype: Bucket
user_core_view_finance_calc_nioapp_90d_cnt:
  bin_boundarie:
  - 0.5
  - 1.5
  - 2.5
  - 3.5
  - 5.5
  - 8.5
  dtype: Bucket
user_core_view_mate_materials_nio_180d_cnt:
  bin_boundarie:
  - 0.5
  dtype: Bucket
user_core_view_mate_materials_nio_1d_cnt:
  bin_boundarie:
  - 0.5
  dtype: Bucket
user_core_view_mate_materials_nio_30d_cnt:
  bin_boundarie:
  - 0.5
  dtype: Bucket
user_core_view_mate_materials_nio_60d_cnt:
  bin_boundarie:
  - 0.5
  dtype: Bucket
user_core_view_mate_materials_nio_7d_cnt:
  bin_boundarie:
  - 0.5
  dtype: Bucket
user_core_view_mate_materials_nio_90d_cnt:
  bin_boundarie:
  - 0.5
  dtype: Bucket
user_core_view_mileage_calc_nioapp_180d_cnt:
  bin_boundarie:
  - 0.5
  - 1.5
  dtype: Bucket
user_core_view_mileage_calc_nioapp_1d_cnt:
  bin_boundarie:
  - 0.5
  dtype: Bucket
user_core_view_mileage_calc_nioapp_30d_cnt:
  bin_boundarie:
  - 0.5
  - 1.5
  dtype: Bucket
user_core_view_mileage_calc_nioapp_60d_cnt:
  bin_boundarie:
  - 0.5
  - 1.5
  dtype: Bucket
user_core_view_mileage_calc_nioapp_7d_cnt:
  bin_boundarie:
  - 0.5
  - 1.5
  dtype: Bucket
user_core_view_mileage_calc_nioapp_90d_cnt:
  bin_boundarie:
  - 0.5
  - 1.5
  dtype: Bucket
