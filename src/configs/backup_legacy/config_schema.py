"""
配置模式定义 - 解决配置文件过于庞大且缺乏验证的问题
"""
from typing import Dict, List, Optional, Union, Any
from dataclasses import dataclass, field
import json

@dataclass
class FeatureConfig:
    """特征配置基类"""
    type: str  # "StringLookup", "Bucket", "Dense", "Embedding", "VarLen"
    dimension: Optional[int] = None
    vocabulary_size: Optional[int] = None
    boundaries: Optional[List[float]] = None
    max_length: Optional[int] = None
    
    def validate(self):
        """验证配置合理性"""
        if self.type == "StringLookup" and not self.vocabulary_size:
            raise ValueError(f"StringLookup feature requires vocabulary_size")
        if self.type == "Bucket" and not self.boundaries:
            raise ValueError(f"Bucket feature requires boundaries")

@dataclass 
class InputModuleConfig:
    """输入模块配置"""
    features: List[str]
    description: str = ""

@dataclass
class SequenceSetConfig:
    """序列集合配置"""
    features: List[str]
    max_length: int = 50
    gru_dimension: int = 32

@dataclass
class ModelArchConfig:
    """模型架构配置 - 分离架构参数和特征定义"""
    network_name: str
    output_dimension: int = 6
    output_activation: str = "sigmoid"
    default_embedding_dimension: int = 8
    default_gru_dimension: int = 32
    expert_num: int = 8
    use_cross_layer: bool = True
    use_multitask: bool = False
    use_time_attention: bool = True
    time_decay_factor: float = 0.05

@dataclass
class DataConfig:
    """数据配置 - 分离数据处理参数"""
    train_dates: List[str]
    test_dates: List[str]
    mask_label: Optional[str] = None
    data_format: str = "parquet"
    partition_column: str = "datetime"

@dataclass
class ExperimentConfig:
    """实验配置 - 管理实验参数"""
    experiment_name: str
    description: str = ""
    model_config: ModelArchConfig = field(default_factory=ModelArchConfig)
    data_config: DataConfig = field(default_factory=DataConfig)
    features: Dict[str, FeatureConfig] = field(default_factory=dict)
    input_modules: Dict[str, InputModuleConfig] = field(default_factory=dict)
    sequence_sets: Dict[str, SequenceSetConfig] = field(default_factory=dict)
    
    def validate(self):
        """验证整个配置的一致性"""
        # 验证特征配置
        for name, feature_config in self.features.items():
            feature_config.validate()
        
        # 验证特征引用一致性
        all_referenced_features = set()
        for module_config in self.input_modules.values():
            all_referenced_features.update(module_config.features)
        
        for seq_config in self.sequence_sets.values():
            all_referenced_features.update(seq_config.features)
        
        undefined_features = all_referenced_features - set(self.features.keys())
        if undefined_features:
            raise ValueError(f"Undefined features referenced: {undefined_features}")

class ConfigFactory:
    """配置工厂 - 提供配置模板和转换工具"""
    
    @classmethod
    def from_legacy_config(cls, legacy_config: dict) -> ExperimentConfig:
        """从旧版配置转换为新版配置"""
        # 提取模型架构参数
        model_config = ModelArchConfig(
            network_name=legacy_config.get("network_name", "EPMMOENet"),
            output_dimension=legacy_config.get("output_dimension", 6),
            output_activation=legacy_config.get("output_activation", "sigmoid")
        )
        
        # 提取数据配置
        data_config = DataConfig(
            train_dates=legacy_config.get("train_dates", []),
            test_dates=legacy_config.get("test_dates", []),
            mask_label=legacy_config.get("mask_label")
        )
        
        # 转换特征配置
        features = {}
        raw_features = legacy_config.get("RawFeature", {})
        for feature_name, feature_info in raw_features.items():
            features[feature_name] = FeatureConfig(**feature_info)
        
        # 转换输入模块配置
        input_modules = {}
        for module_name in ["InputGeneral", "InputScene"]:
            if module_name in legacy_config:
                input_modules[module_name] = InputModuleConfig(
                    features=legacy_config[module_name].get("features", [])
                )
        
        # 转换序列集合配置
        sequence_sets = {}
        if "InputSeqSet" in legacy_config:
            seq_info = legacy_config["InputSeqSet"].get("SetInfo", {})
            for set_name, set_config in seq_info.items():
                sequence_sets[set_name] = SequenceSetConfig(
                    features=set_config.get("features", []),
                    max_length=set_config.get("max_length", 50)
                )
        
        return ExperimentConfig(
            experiment_name="converted_from_legacy",
            model_config=model_config,
            data_config=data_config,
            features=features,
            input_modules=input_modules,
            sequence_sets=sequence_sets
        )
    
    @classmethod
    def create_baseline_template(cls) -> ExperimentConfig:
        """创建基线模型配置模板"""
        return ExperimentConfig(
            experiment_name="baseline_template",
            description="基础转化率预测模型模板",
            model_config=ModelArchConfig(
                network_name="EPMMOENet",
                use_cross_layer=True,
                use_time_attention=True
            ),
            data_config=DataConfig(
                train_dates=["20240430"],
                test_dates=["20240531"]
            )
        )
    
    def add_embedding_features(self, config: ExperimentConfig, 
                              embedding_features: List[str], 
                              embedding_dim: int = 16) -> ExperimentConfig:
        """为配置添加嵌入特征支持"""
        # 添加嵌入特征定义
        for feature_name in embedding_features:
            config.features[feature_name] = FeatureConfig(
                type="Dense",  # 嵌入特征使用Dense层
                dimension=embedding_dim
            )
        
        # 添加到InputGeneral模块
        if "InputGeneral" not in config.input_modules:
            config.input_modules["InputGeneral"] = InputModuleConfig(features=[])
        
        config.input_modules["InputGeneral"].features.extend(embedding_features)
        
        return config