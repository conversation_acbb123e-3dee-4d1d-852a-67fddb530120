# nio_v7_converted 使用示例

## 1. 使用分层配置创建模型

```python
from src.configs.config_manager_enhanced import config_manager

# 创建分层配置
config = config_manager.create_layered_config(
    architecture="nio_v7_converted_arch",
    feature_groups=['general_contextual', 'general_behavioral', 'general_demographic'],  # 使用前3个特征组作为示例
    data_config="nio_v7_converted_data",
    experiment_name="nio_v7_converted_experiment"
)

# 使用新的模型工厂创建模型
from src.models.model_factory import create_model
model = create_model("EPMMOENet_Enhanced", config.model_config)
```

## 2. 使用增强版训练器

```python
from src.training.enhanced_trainer import EnhancedModelTrainer

trainer = EnhancedModelTrainer(config.model_config, "nio_v7_converted_run")
model = trainer.build_model(feature_dict)
trainer.train(train_dataset, val_dataset, epochs=50)
```

## 3. 可用的特征组

- general_contextual: 从legacy配置提取的general_contextual特征组 (12 个特征)
- general_behavioral: 从legacy配置提取的general_behavioral特征组 (308 个特征)
- general_demographic: 从legacy配置提取的general_demographic特征组 (7 个特征)
- general_temporal: 从legacy配置提取的general_temporal特征组 (2 个特征)
- scene_demographic: 从legacy配置提取的scene_demographic特征组 (1 个特征)
- scene_contextual: 从legacy配置提取的scene_contextual特征组 (3 个特征)
- scene_behavioral: 从legacy配置提取的scene_behavioral特征组 (2 个特征)
- sequence_main_seq: 从legacy配置提取的sequence_main_seq特征组 (2 个特征)
- sequence_car_seq: 从legacy配置提取的sequence_car_seq特征组 (3 个特征)

## 4. 配置文件结构

```
src/configs/
├── architectures/nio_v7_converted_arch.yaml     # 架构配置
├── data/nio_v7_converted_data.yaml              # 数据配置  
├── features/nio_v7_converted_feature_groups.yaml # 特征组配置
├── features/nio_v7_converted_features_chunk_*.yaml # 特征定义（分块）
└── experiments/nio_v7_converted.yaml             # 实验配置
```

## 5. 迁移对比

### 原方式（问题）：
- 单个4135行的JSON文件
- 架构、特征、数据配置混杂
- 难以维护和版本管理
- 团队协作冲突频繁

### 新方式（解决）：
- 按职责分离的多个小文件
- 清晰的配置层次结构
- 易于维护和扩展
- 支持配置继承和组合
