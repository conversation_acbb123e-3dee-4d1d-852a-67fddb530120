"""
增强配置管理器 - 解决巨大JSON配置文件问题

核心改进：
1. 按职责分离配置（架构/特征/数据/实验）
2. 支持配置继承和组合
3. 自动验证和类型检查
4. 配置模板化和版本管理
"""

import json
import yaml
from pathlib import Path
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass, asdict
import logging
from datetime import datetime

from src.configs.config_schema import (
    ExperimentConfig, ModelArchConfig, DataConfig, 
    FeatureConfig, ConfigFactory
)

class ConfigurationError(Exception):
    """配置错误异常"""
    pass

@dataclass
class FeatureGroup:
    """特征组定义"""
    name: str
    description: str
    features: List[str]
    category: str  # "behavioral", "demographic", "contextual", etc.

class FeatureRegistry:
    """
    特征注册表 - 管理特征定义的中央仓库
    
    解决问题：
    - 特征定义重复和不一致
    - 特征文档缺失
    - 特征依赖管理困难
    """
    
    def __init__(self, registry_path: Optional[str] = None):
        self.registry_path = registry_path or "src/configs/features"
        self.logger = logging.getLogger(__name__)
        self.features = {}
        self.feature_groups = {}
        
        self._load_feature_registry()
    
    def _load_feature_registry(self):
        """加载特征注册表"""
        registry_dir = Path(self.registry_path)
        
        if not registry_dir.exists():
            self.logger.warning(f"Feature registry directory not found: {registry_dir}")
            return
        
        # 加载特征组定义
        groups_file = registry_dir / "feature_groups.yaml"
        if groups_file.exists():
            with open(groups_file, 'r', encoding='utf-8') as f:
                groups_data = yaml.safe_load(f)
                for group_name, group_info in groups_data.items():
                    self.feature_groups[group_name] = FeatureGroup(
                        name=group_name,
                        description=group_info.get('description', ''),
                        features=group_info.get('features', []),
                        category=group_info.get('category', 'unknown')
                    )
        
        # 加载特征定义
        features_file = registry_dir / "feature_definitions.yaml"
        if features_file.exists():
            with open(features_file, 'r', encoding='utf-8') as f:
                self.features = yaml.safe_load(f)
        
        self.logger.info(f"Loaded {len(self.features)} features and {len(self.feature_groups)} feature groups")
    
    def get_feature_config(self, feature_name: str) -> Optional[FeatureConfig]:
        """获取特征配置"""
        if feature_name in self.features:
            feature_data = self.features[feature_name]
            return FeatureConfig(**feature_data)
        return None
    
    def get_feature_group(self, group_name: str) -> Optional[FeatureGroup]:
        """获取特征组"""
        return self.feature_groups.get(group_name)
    
    def list_features_by_category(self, category: str) -> List[str]:
        """按类别列出特征"""
        features = []
        for group in self.feature_groups.values():
            if group.category == category:
                features.extend(group.features)
        return features
    
    def validate_feature_list(self, feature_list: List[str]) -> List[str]:
        """验证特征列表，返回未定义的特征"""
        undefined_features = []
        for feature in feature_list:
            if feature not in self.features:
                undefined_features.append(feature)
        return undefined_features

class HierarchicalConfigManager:
    """
    分层配置管理器 - 主要的配置管理类
    
    配置层次结构：
    1. 基础架构配置（模型类型、基本参数）
    2. 特征配置（特征定义和分组）
    3. 数据配置（数据源、预处理参数）
    4. 实验配置（具体实验设置）
    """
    
    def __init__(self, config_root: str = "src/configs"):
        self.config_root = Path(config_root)
        self.logger = logging.getLogger(__name__)
        
        # 初始化各个配置目录
        self.architecture_dir = self.config_root / "architectures"
        self.features_dir = self.config_root / "features"
        self.data_dir = self.config_root / "data"
        self.experiments_dir = self.config_root / "experiments"
        
        # 创建目录结构
        self._ensure_directories()
        
        # 初始化特征注册表
        self.feature_registry = FeatureRegistry(str(self.features_dir))
        
        # 初始化配置工厂
        self.config_factory = ConfigFactory()
    
    def _ensure_directories(self):
        """确保配置目录存在"""
        for directory in [self.architecture_dir, self.features_dir, 
                         self.data_dir, self.experiments_dir]:
            directory.mkdir(parents=True, exist_ok=True)
    
    def create_layered_config(self, 
                            architecture_name: str,
                            feature_groups: List[str],
                            data_config_name: str,
                            experiment_name: str,
                            **overrides) -> ExperimentConfig:
        """
        创建分层配置
        
        Args:
            architecture_name: 架构配置名称
            feature_groups: 特征组列表
            data_config_name: 数据配置名称
            experiment_name: 实验名称
            **overrides: 覆盖参数
            
        Returns:
            完整的实验配置
        """
        try:
            # 1. 加载架构配置
            arch_config = self._load_architecture_config(architecture_name)
            
            # 2. 组合特征配置
            features_config = self._compose_features_config(feature_groups)
            
            # 3. 加载数据配置
            data_config = self._load_data_config(data_config_name)
            
            # 4. 创建实验配置
            experiment_config = ExperimentConfig(
                experiment_name=experiment_name,
                description=f"Generated config for {experiment_name}",
                model_config=arch_config,
                data_config=data_config,
                features=features_config["features"],
                input_modules=features_config["input_modules"],
                sequence_sets=features_config["sequence_sets"]
            )
            
            # 5. 应用覆盖参数
            if overrides:
                experiment_config = self._apply_overrides(experiment_config, overrides)
            
            # 6. 验证配置
            experiment_config.validate()
            
            self.logger.info(f"Created layered config for experiment: {experiment_name}")
            return experiment_config
            
        except Exception as e:
            raise ConfigurationError(f"Failed to create layered config: {e}")
    
    def _load_architecture_config(self, architecture_name: str) -> ModelArchConfig:
        """加载架构配置"""
        arch_file = self.architecture_dir / f"{architecture_name}.yaml"
        
        if not arch_file.exists():
            # 如果没有找到，创建默认配置
            self.logger.warning(f"Architecture config not found: {arch_file}, using defaults")
            return ModelArchConfig(network_name=architecture_name)
        
        with open(arch_file, 'r', encoding='utf-8') as f:
            arch_data = yaml.safe_load(f)
        
        return ModelArchConfig(**arch_data)
    
    def _compose_features_config(self, feature_groups: List[str]) -> Dict[str, Any]:
        """组合特征配置"""
        all_features = {}
        input_modules = {}
        sequence_sets = {}
        
        for group_name in feature_groups:
            feature_group = self.feature_registry.get_feature_group(group_name)
            
            if not feature_group:
                self.logger.warning(f"Feature group not found: {group_name}")
                continue
            
            # 添加该组的特征定义
            for feature_name in feature_group.features:
                feature_config = self.feature_registry.get_feature_config(feature_name)
                if feature_config:
                    all_features[feature_name] = feature_config
            
            # 根据特征组类别决定输入模块
            if feature_group.category in ["behavioral", "demographic"]:
                if "InputGeneral" not in input_modules:
                    input_modules["InputGeneral"] = {"features": []}
                input_modules["InputGeneral"]["features"].extend(feature_group.features)
            
            elif feature_group.category == "sequential":
                sequence_sets[group_name] = {
                    "features": feature_group.features,
                    "max_length": 50  # 默认值，可以从配置中获取
                }
            
            elif feature_group.category == "contextual":
                if "InputScene" not in input_modules:
                    input_modules["InputScene"] = {"features": []}
                input_modules["InputScene"]["features"].extend(feature_group.features)
        
        return {
            "features": all_features,
            "input_modules": input_modules,
            "sequence_sets": sequence_sets
        }
    
    def _load_data_config(self, data_config_name: str) -> DataConfig:
        """加载数据配置"""
        data_file = self.data_dir / f"{data_config_name}.yaml"
        
        if not data_file.exists():
            self.logger.warning(f"Data config not found: {data_file}, using defaults")
            return DataConfig(train_dates=["20240430"], test_dates=["20240531"])
        
        with open(data_file, 'r', encoding='utf-8') as f:
            data_config_data = yaml.safe_load(f)
        
        return DataConfig(**data_config_data)
    
    def _apply_overrides(self, config: ExperimentConfig, overrides: Dict[str, Any]) -> ExperimentConfig:
        """应用覆盖参数"""
        # 这里可以实现复杂的覆盖逻辑
        # 简单实现：直接更新对应字段
        
        for key, value in overrides.items():
            if hasattr(config, key):
                setattr(config, key, value)
            elif hasattr(config.model_config, key):
                setattr(config.model_config, key, value)
            elif hasattr(config.data_config, key):
                setattr(config.data_config, key, value)
        
        return config
    
    def save_experiment_config(self, config: ExperimentConfig, 
                             experiment_name: Optional[str] = None) -> Path:
        """保存实验配置"""
        exp_name = experiment_name or config.experiment_name
        exp_file = self.experiments_dir / f"{exp_name}.yaml"
        
        # 转换为字典并保存
        config_dict = asdict(config)
        
        with open(exp_file, 'w', encoding='utf-8') as f:
            yaml.dump(config_dict, f, default_flow_style=False, allow_unicode=True)
        
        self.logger.info(f"Experiment config saved: {exp_file}")
        return exp_file
    
    def load_experiment_config(self, experiment_name: str) -> ExperimentConfig:
        """加载实验配置"""
        exp_file = self.experiments_dir / f"{experiment_name}.yaml"
        
        if not exp_file.exists():
            raise ConfigurationError(f"Experiment config not found: {exp_file}")
        
        with open(exp_file, 'r', encoding='utf-8') as f:
            config_data = yaml.safe_load(f)
        
        # 重建配置对象
        return self._reconstruct_experiment_config(config_data)
    
    def _reconstruct_experiment_config(self, config_data: Dict[str, Any]) -> ExperimentConfig:
        """从字典重建实验配置对象"""
        # 重建嵌套对象
        model_config = ModelArchConfig(**config_data["model_config"])
        data_config = DataConfig(**config_data["data_config"])
        
        # 重建特征配置
        features = {}
        for name, feature_data in config_data["features"].items():
            features[name] = FeatureConfig(**feature_data)
        
        return ExperimentConfig(
            experiment_name=config_data["experiment_name"],
            description=config_data.get("description", ""),
            model_config=model_config,
            data_config=data_config,
            features=features,
            input_modules=config_data.get("input_modules", {}),
            sequence_sets=config_data.get("sequence_sets", {})
        )
    
    def convert_legacy_config(self, legacy_config_path: str, 
                            new_experiment_name: str) -> ExperimentConfig:
        """
        转换旧版配置到新版分层配置
        
        这是关键的迁移功能！
        """
        self.logger.info(f"Converting legacy config: {legacy_config_path}")
        
        # 加载旧版配置
        with open(legacy_config_path, 'r', encoding='utf-8') as f:
            legacy_config = json.load(f)
        
        # 使用ConfigFactory转换
        experiment_config = self.config_factory.from_legacy_config(legacy_config)
        experiment_config.experiment_name = new_experiment_name
        
        # 分析并分离特征
        self._extract_and_save_features(legacy_config, new_experiment_name)
        
        # 保存新配置
        self.save_experiment_config(experiment_config, new_experiment_name)
        
        self.logger.info(f"Legacy config converted successfully: {new_experiment_name}")
        return experiment_config
    
    def _extract_and_save_features(self, legacy_config: Dict[str, Any], 
                                 experiment_name: str):
        """从旧配置中提取并保存特征定义"""
        raw_features = legacy_config.get("RawFeature", {})
        
        # 按类别分组特征
        feature_groups_by_category = {}
        
        for feature_name, feature_info in raw_features.items():
            # 简单的特征分类逻辑（可以改进）
            if "seq" in feature_name.lower():
                category = "sequential"
            elif any(keyword in feature_name.lower() for keyword in ["age", "gender", "identity"]):
                category = "demographic"  
            elif any(keyword in feature_name.lower() for keyword in ["intention", "stage", "scene"]):
                category = "contextual"
            else:
                category = "behavioral"
            
            if category not in feature_groups_by_category:
                feature_groups_by_category[category] = []
            
            feature_groups_by_category[category].append(feature_name)
        
        # 保存特征组定义
        groups_file = self.features_dir / f"{experiment_name}_feature_groups.yaml"
        with open(groups_file, 'w', encoding='utf-8') as f:
            yaml.dump(feature_groups_by_category, f, default_flow_style=False, allow_unicode=True)
        
        # 保存特征定义
        features_file = self.features_dir / f"{experiment_name}_features.yaml"
        with open(features_file, 'w', encoding='utf-8') as f:
            yaml.dump(raw_features, f, default_flow_style=False, allow_unicode=True)
        
        self.logger.info(f"Extracted {len(raw_features)} features into {len(feature_groups_by_category)} categories")

# 全局配置管理器实例
config_manager = HierarchicalConfigManager()

def create_layered_config(architecture: str, 
                         feature_groups: List[str],
                         data_config: str,
                         experiment_name: str,
                         **kwargs) -> ExperimentConfig:
    """便捷的分层配置创建函数"""
    return config_manager.create_layered_config(
        architecture, feature_groups, data_config, experiment_name, **kwargs)

def convert_legacy_config(legacy_path: str, new_name: str) -> ExperimentConfig:
    """便捷的配置转换函数"""
    return config_manager.convert_legacy_config(legacy_path, new_name)