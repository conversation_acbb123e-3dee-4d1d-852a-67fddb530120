architecture_config: nio_v7_converted_arch.yaml
conversion_date: '2025-01-13'
data_config: nio_v7_converted_data.yaml
description: 从legacy JSON转换的实验配置：nio_v7_converted
experiment_name: nio_v7_converted
feature_groups_config: nio_v7_converted_feature_groups.yaml
feature_groups_to_use:
- general_contextual
- general_behavioral
- general_demographic
- general_temporal
- scene_demographic
- scene_contextual
- scene_behavioral
- sequence_main_seq
- sequence_car_seq
legacy_info:
  network_name: EPMMOENet
  original_file: sample_20250311_v7-20250311.json
  test_dates:
  - '20240531'
  total_features: 355
  train_dates:
  - '20240430'
training_params:
  batch_size: 4096
  epochs: 50
  learning_rate: 0.0005
  patience: 10
version: converted_v1.0
