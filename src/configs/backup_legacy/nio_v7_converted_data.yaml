batch_loading: true
conversion_date: '2025-01-13'
data_format: parquet
data_root: data/dataset_nio_new_car_v15
description: 从nio_v7_converted转换的数据配置
mask_label: mask_label
num_parallel_reads: 4
partition_column: datetime
preprocessing:
  categorical_missing_token: <UNK>
  default_sequence_length: 50
  max_categories_per_feature: 10000
  min_category_frequency: 10
  missing_value_strategy: fill_default
  numerical_missing_value: 0
  numerical_scaling: none
  outlier_detection: true
  outlier_threshold: 3.0
  remove_duplicates: true
  sequence_padding: post
  sequence_truncating: post
random_seed: 42
target_labels:
- m_purchase_days_nio_new_car
- d_purchase_days_nio_new_car
- pos_flag
test_dates:
- '20240531'
train_dates:
- '20240430'
validation_split: 0.2
version: converted_v1.0
