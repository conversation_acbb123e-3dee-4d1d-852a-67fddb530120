# NIO新车数据集配置
# 只包含数据相关的参数，不包含特征定义

# 数据日期设置
train_dates:
  - "20240430"

test_dates:
  - "20240531"

# 数据源配置
data_format: "parquet"
partition_column: "datetime"
data_root: "data/dataset_nio_new_car_v15"

# 数据预处理参数
preprocessing:
  # 缺失值处理
  missing_value_strategy: "fill_default"
  categorical_missing_token: "<UNK>"
  numerical_missing_value: 0
  
  # 数据清理
  remove_duplicates: true
  outlier_detection: true
  outlier_threshold: 3.0  # z-score阈值
  
  # 类别特征处理
  min_category_frequency: 10  # 最小类别频次
  max_categories_per_feature: 10000  # 每个特征最大类别数
  
  # 数值特征处理
  numerical_scaling: "none"  # "none", "standard", "minmax"
  
  # 序列特征处理
  sequence_padding: "post"
  sequence_truncating: "post"
  default_sequence_length: 50

# 标签配置
mask_label: "mask_label"
target_labels:
  - "m_purchase_days_nio_new_car"

# 数据分割
validation_split: 0.2
random_seed: 42

# 数据加载配置
batch_loading: true
num_parallel_reads: 4

# 描述信息
description: "NIO新车转化率预测数据集配置"
version: "v15"
dataset_size: "约100万样本"
feature_count: "300+个特征"
created_date: "2025-01-13"