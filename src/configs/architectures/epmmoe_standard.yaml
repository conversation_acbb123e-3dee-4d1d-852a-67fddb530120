# EPMMOENet标准架构配置
# 只包含模型架构相关的参数，不包含特征定义

network_name: "EPMMOENet"
output_dimension: 6
output_activation: "sigmoid"

# 基础架构参数
default_embedding_dimension: 8
default_gru_dimension: 32
expert_num: 8

# 架构开关
use_cross_layer: true
use_multitask: false
use_mixed_precision: true
use_time_attention: true
time_decay_factor: 0.05

# 训练相关参数
loss_type: "standard"
pos_weight: 10.0
use_month_weights: false
val_metric: "loss"

# 描述信息
description: "EPMMOENet标准架构 - 支持多模态特征和时间注意力机制"
version: "v1.0"
author: "NIO ML Team"
created_date: "2025-01-13"