# EPMMOENet支持嵌入特征的架构配置

network_name: "EPMMOENet_with_embeddings"
output_dimension: 6
output_activation: "sigmoid"

# 基础架构参数
default_embedding_dimension: 8
default_gru_dimension: 32
expert_num: 8

# 架构开关
use_cross_layer: true
use_multitask: false
use_mixed_precision: true
use_time_attention: true
time_decay_factor: 0.05

# 嵌入特征特定参数
supports_embeddings: true
default_embedding_feature_dim: 16
embedding_integration_method: "concatenate"

# 训练相关参数
loss_type: "standard"
pos_weight: 10.0
use_month_weights: false
val_metric: "loss"

# 描述信息
description: "EPMMOENet嵌入版架构 - 支持预训练嵌入向量集成"
version: "v1.0"
author: "NIO ML Team"
created_date: "2025-01-13"