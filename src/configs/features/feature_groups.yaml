# 特征分组定义
# 将300+个特征按业务逻辑分组，便于管理和实验

# 用户核心行为特征组
user_core_behavior:
  description: "用户在NIO平台的核心行为统计特征"
  category: "behavioral"
  features:
    # 搜索相关
    - "user_core_search_nioapp_1d_cnt"
    - "user_core_search_nioapp_7d_cnt"
    - "user_core_search_nioapp_30d_cnt"
    - "user_core_search_nioapp_60d_cnt"
    - "user_core_search_nioapp_90d_cnt"
    - "user_core_search_nioapp_180d_cnt"
    # 登录相关
    - "user_core_visit_nioapp_login_1d_cnt"
    - "user_core_visit_nioapp_login_7d_cnt"
    - "user_core_visit_nioapp_login_30d_cnt"
    - "user_core_visit_nioapp_login_60d_cnt"
    - "user_core_visit_nioapp_login_90d_cnt"
    - "user_core_visit_nioapp_login_180d_cnt"

# 购车意向行为特征组
purchase_intention_behavior:
  description: "与购车意向直接相关的行为特征"
  category: "behavioral"
  features:
    # 车型配置相关
    - "user_core_view_veh_cfg_params_nioapp_1d_cnt"
    - "user_core_view_veh_cfg_params_nioapp_7d_cnt"
    - "user_core_view_veh_cfg_params_nioapp_30d_cnt"
    - "user_core_view_veh_cfg_params_nioapp_60d_cnt"
    - "user_core_view_veh_cfg_params_nioapp_90d_cnt"
    - "user_core_view_veh_cfg_params_nioapp_180d_cnt"
    # 车型介绍相关
    - "user_core_view_veh_intro_nioapp_1d_cnt"
    - "user_core_view_veh_intro_nioapp_7d_cnt"
    - "user_core_view_veh_intro_nioapp_30d_cnt"
    - "user_core_view_veh_intro_nioapp_60d_cnt"
    - "user_core_view_veh_intro_nioapp_90d_cnt"
    - "user_core_view_veh_intro_nioapp_180d_cnt"
    # 金融计算相关
    - "user_core_view_finance_calc_nioapp_1d_cnt"
    - "user_core_view_finance_calc_nioapp_7d_cnt"
    - "user_core_view_finance_calc_nioapp_30d_cnt"
    - "user_core_view_finance_calc_nioapp_60d_cnt"
    - "user_core_view_finance_calc_nioapp_90d_cnt"
    - "user_core_view_finance_calc_nioapp_180d_cnt"

# 服务体验行为特征组
service_experience_behavior:
  description: "用户对NIO服务的体验和使用行为"
  category: "behavioral"
  features:
    # 充电体验
    - "user_core_exp_charging_nio_1d_cnt"
    - "user_core_exp_charging_nio_7d_cnt"
    - "user_core_exp_charging_nio_30d_cnt"
    - "user_core_exp_charging_nio_60d_cnt"
    - "user_core_exp_charging_nio_90d_cnt"
    - "user_core_exp_charging_nio_180d_cnt"
    # 维保体验
    - "user_core_exp_maintenance_nio_1d_cnt"
    - "user_core_exp_maintenance_nio_7d_cnt"
    - "user_core_exp_maintenance_nio_30d_cnt"
    - "user_core_exp_maintenance_nio_60d_cnt"
    - "user_core_exp_maintenance_nio_90d_cnt"
    - "user_core_exp_maintenance_nio_180d_cnt"

# 线下互动特征组
offline_interaction:
  description: "用户线下互动和面对面交流行为"
  category: "behavioral"
  features:
    # 面对面访谈
    - "user_core_fl_f2f_interview_nio_1d_cnt"
    - "user_core_fl_f2f_interview_nio_7d_cnt"
    - "user_core_fl_f2f_interview_nio_30d_cnt"
    - "user_core_fl_f2f_interview_nio_60d_cnt"
    - "user_core_fl_f2f_interview_nio_90d_cnt"
    - "user_core_fl_f2f_interview_nio_180d_cnt"
    # 线下即时通讯
    - "user_core_fl_offline_im_nio_1d_cnt"
    - "user_core_fl_offline_im_nio_7d_cnt"
    - "user_core_fl_offline_im_nio_30d_cnt"
    - "user_core_fl_offline_im_nio_60d_cnt"
    - "user_core_fl_offline_im_nio_90d_cnt"
    - "user_core_fl_offline_im_nio_180d_cnt"

# 意向阶段特征组
intention_stage:
  description: "用户当前的购车意向阶段标识"
  category: "contextual"
  features:
    - "intention_stage"
    - "fellow_follow_decision_maker"
    - "fellow_follow_intention_nio_confirm"
    - "fellow_follow_intention_test_drive"

# 用户属性特征组
user_demographics:
  description: "用户的基本属性和身份特征"
  category: "demographic"
  features:
    - "user_core_user_age_group"
    - "user_core_user_gender"
    - "user_core_resident_city"
    - "user_core_pred_career_type"
    - "user_core_nio_user_identity"
    - "user_core_is_nio_employee"

# 用户行为序列特征组
user_behavior_sequence:
  description: "用户行为的时间序列特征"
  category: "sequential"
  features:
    - "user_core_action_code_seq"
    - "user_core_action_day_seq"

# 车型相关序列特征组
car_behavior_sequence:
  description: "用户与车型相关的行为序列"
  category: "sequential"
  features:
    - "user_car_core_action_code_seq"
    - "user_car_core_action_day_seq"
    - "user_car_core_action_veh_model_seq"

# APP搜索意向特征组
app_search_intention:
  description: "用户在APP中的搜索意向特征"
  category: "behavioral"
  features:
    - "app_search_intention_cnt_1d"
    - "app_search_intention_cnt_7d"
    - "app_search_intention_cnt_14d"
    - "app_search_intention_cnt_30d"
    - "app_search_intention_cnt_60d"
    - "app_search_intention_cnt_90d"
    - "app_search_intention_cnt_180d"
    - "app_search_intention_DSLA"

# 线索注册特征组
leads_registration:
  description: "用户线索注册相关特征"
  category: "behavioral"
  features:
    - "user_core_first_reg_leads_nio_DSLA"
    - "user_core_unfirst_reg_leads_nio_1d_cnt"