{"network_name": "EPMMOENet", "train_dates": ["20240430"], "test_dates": ["20240531"], "mask_label": null, "InputGeneral": {"features": ["fellow_follow_decision_maker", "fellow_follow_intention_nio_confirm", "fellow_follow_intention_test_drive", "user_core_first_reg_leads_nio_DSLA", "app_search_intention_cnt_1d"]}, "InputScene": {"features": ["intention_stage"]}, "InputSeqSet": {"Set": ["main_seq"], "SetInfo": {"main_seq": {"name": "核心-核心行为", "features": ["user_core_action_code_seq", "user_core_action_day_seq"]}}}, "RawLabel": {"m_purchase_days_nio_new_car": {}, "d_purchase_days_nio_new_car": {}, "pos_flag": {}}, "RawFeature": {"fellow_follow_decision_maker": {"dtype": "StringLookup", "vocabulary": ["0", "1", "2", "3", "4", "5", "99", "LowFreq", "None"]}, "fellow_follow_intention_nio_confirm": {"dtype": "StringLookup", "vocabulary": ["0", "1", "2", "3", "LowFreq", "None"]}, "fellow_follow_intention_test_drive": {"dtype": "StringLookup", "vocabulary": ["0", "1", "2", "3", "LowFreq", "None"]}, "user_core_first_reg_leads_nio_DSLA": {"dtype": "Bucket", "bin_boundarie": [1.5, 3.5, 5.5, 7.5, 10.5, 13.5, 15.5, 17.5, 20.5, 22.5, 25.5, 27.5, 28.5, 35.5, 47.5, 62.5, 77.5, 87.5, 111.5, 123.5, 143.5, 160.5, 179.5]}, "app_search_intention_cnt_1d": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5]}, "intention_stage": {"dtype": "StringLookup", "vocabulary": ["A", "I", "D", "O", "None"]}, "user_core_action_code_seq": {"type": "VarLen", "dtype": "StringLookup", "max_len": 50, "padd_value": "0", "vocabulary": ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "None"]}, "user_core_action_day_seq": {"type": "VarLen", "dtype": "<PERSON><PERSON>", "max_len": 50, "padd_value": 0}}}