{"network_name": "EPMMOENet", "train_dates": ["20240430"], "test_dates": ["20240531"], "mask_label": null, "use_multitask": false, "InputGeneral": {"features": ["fellow_follow_decision_maker", "fellow_follow_intention_nio_confirm", "fellow_follow_intention_test_drive", "user_core_user_gender", "user_core_user_age_group", "user_core_resident_city", "user_core_is_nio_employee", "user_core_pred_career_type", "user_core_nio_user_identity", "intention_stage", "intention_status", "intention_create_time_days", "user_create_days", "user_register_days", "user_core_answer_sales_pc_nio_1d_cnt", "user_core_answer_sales_pc_nio_30d_cnt", "user_core_fl_f2f_interview_nio_1d_cnt", "user_core_fl_f2f_interview_nio_30d_cnt", "user_core_fl_offline_im_nio_1d_cnt", "user_core_fl_offline_im_nio_30d_cnt", "user_core_save_veh_cgf_nio_1d_cnt", "user_core_save_veh_cgf_nio_30d_cnt", "user_core_del_veh_cgf_nio_1d_cnt", "user_core_del_veh_cgf_nio_30d_cnt", "user_core_view_used_veh_1d_cnt", "user_core_view_used_veh_30d_cnt", "user_core_search_nioapp_1d_cnt", "user_core_search_nioapp_30d_cnt", "user_core_view_cm_hp_nioapp_1d_cnt", "user_core_view_cm_hp_nioapp_30d_cnt", "user_core_view_cm_dp_nioapp_1d_cnt", "user_core_view_cm_dp_nioapp_30d_cnt", "user_core_book_td_nio_1d_cnt", "user_core_book_td_nio_30d_cnt", "user_core_exp_td_nio_1d_cnt", "user_core_exp_td_nio_30d_cnt", "user_core_visit_nh_1d_cnt", "user_core_visit_nh_30d_cnt", "user_core_exp_charging_nio_1d_cnt", "user_core_exp_charging_nio_30d_cnt", "user_core_lock_ncar_nio_1d_cnt", "user_core_lock_ncar_nio_30d_cnt", "user_core_pay_ncar_dp_nio_1d_cnt", "user_core_pay_ncar_dp_nio_30d_cnt", "user_core_exp_maintenance_nio_1d_cnt", "user_core_exp_maintenance_nio_30d_cnt", "user_core_checkin_nioapp_1d_cnt", "user_core_checkin_nioapp_30d_cnt", "user_core_buy_cm_nioapp_1d_cnt", "user_core_buy_cm_nioapp_30d_cnt", "user_core_buy_nl_nioapp_1d_cnt", "user_core_buy_nl_nioapp_30d_cnt"]}, "InputScene": {"features": []}, "InputSeqSet": {"Set": [], "SetInfo": {}}, "RawLabel": {"m_purchase_days_nio_new_car": {}, "d_purchase_days_nio_new_car": {}, "pos_flag": {}}, "RawFeature": {"fellow_follow_decision_maker": {"dtype": "StringLookup", "vocabulary": ["0", "1", "2", "3", "4", "5", "99", "LowFreq", "None"]}, "fellow_follow_intention_nio_confirm": {"dtype": "StringLookup", "vocabulary": ["0", "1", "2", "3", "LowFreq", "None"]}, "fellow_follow_intention_test_drive": {"dtype": "StringLookup", "vocabulary": ["0", "1", "2", "3", "LowFreq", "None"]}, "user_core_user_gender": {"dtype": "StringLookup", "vocabulary": ["M", "F", "Unknown", "None"]}, "user_core_user_age_group": {"dtype": "StringLookup", "vocabulary": ["18-25", "26-30", "31-35", "36-40", "41-45", "46-50", "51+", "Unknown", "None"]}, "user_core_resident_city": {"dtype": "StringLookup", "vocabulary": ["Beijing", "Shanghai", "Guangzhou", "Shenzhen", "Hangzhou", "Chengdu", "Nanjing", "<PERSON><PERSON>", "<PERSON><PERSON>", "Suzhou", "Other", "None"]}, "user_core_is_nio_employee": {"dtype": "StringLookup", "vocabulary": ["0", "1", "None"]}, "user_core_pred_career_type": {"dtype": "StringLookup", "vocabulary": ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "None"]}, "user_core_nio_user_identity": {"dtype": "StringLookup", "vocabulary": ["0", "1", "2", "3", "4", "5", "None"]}, "intention_stage": {"dtype": "StringLookup", "vocabulary": ["A", "I", "D", "O", "None"]}, "intention_status": {"dtype": "StringLookup", "vocabulary": ["Active", "Inactive", "Closed", "None"]}, "user_core_answer_sales_pc_nio_1d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5]}, "user_core_answer_sales_pc_nio_30d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5]}, "user_core_fl_f2f_interview_nio_1d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5]}, "user_core_fl_f2f_interview_nio_30d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5]}, "user_core_fl_offline_im_nio_1d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5]}, "user_core_fl_offline_im_nio_30d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 4.5, 5.5]}, "user_core_save_veh_cgf_nio_1d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 4.5]}, "user_core_save_veh_cgf_nio_30d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 4.5, 5.5, 6.5]}, "user_core_del_veh_cgf_nio_1d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5]}, "user_core_del_veh_cgf_nio_30d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 4.5]}, "user_core_view_used_veh_1d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5]}, "user_core_view_used_veh_30d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 4.5, 5.5]}, "user_core_search_nioapp_1d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 4.5, 5.5]}, "user_core_search_nioapp_30d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 4.5, 5.5, 6.5, 7.5, 8.5]}, "user_core_view_cm_hp_nioapp_1d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5]}, "user_core_view_cm_hp_nioapp_30d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 4.5, 5.5, 6.5, 7.5]}, "user_core_view_cm_dp_nioapp_1d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 4.5, 5.5, 6.5, 7.5, 8.5, 9.5, 10.5]}, "user_core_view_cm_dp_nioapp_30d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 4.5, 5.5, 6.5, 7.5, 8.5, 9.5, 10.5, 11.5, 12.5]}, "user_core_book_td_nio_1d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5]}, "user_core_book_td_nio_30d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5]}, "user_core_exp_td_nio_1d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5]}, "user_core_exp_td_nio_30d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5]}, "user_core_visit_nh_1d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5]}, "user_core_visit_nh_30d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5]}, "user_core_exp_charging_nio_1d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5]}, "user_core_exp_charging_nio_30d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 4.5, 5.5, 6.5]}, "user_core_lock_ncar_nio_1d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5]}, "user_core_lock_ncar_nio_30d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5]}, "user_core_pay_ncar_dp_nio_1d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5]}, "user_core_pay_ncar_dp_nio_30d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5]}, "user_core_exp_maintenance_nio_1d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5]}, "user_core_exp_maintenance_nio_30d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5]}, "user_core_checkin_nioapp_1d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5]}, "user_core_checkin_nioapp_30d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 4.5, 5.5, 6.5, 7.5, 8.5]}, "user_core_buy_cm_nioapp_1d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5]}, "user_core_buy_cm_nioapp_30d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 4.5]}, "user_core_buy_nl_nioapp_1d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5]}, "user_core_buy_nl_nioapp_30d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 4.5]}, "intention_create_time_days": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 3.5, 7.5, 15.5, 30.5, 60.5, 90.5, 180.5, 365.5, 730.5, 1095.5, 1460.5, 1826.5, 2191.5, 2557.5]}, "user_create_days": {"dtype": "Bucket", "bin_boundarie": [0.5, 7.5, 30.5, 90.5, 180.5, 365.5, 730.5, 1095.5, 1460.5, 1826.5, 2191.5, 2557.5, 2922.5, 3287.5, 3653.5]}, "user_register_days": {"dtype": "Bucket", "bin_boundarie": [0.5, 7.5, 30.5, 90.5, 180.5, 365.5, 730.5, 1095.5, 1460.5, 1826.5, 2191.5, 2557.5, 2922.5, 3287.5, 3653.5, 4018.5, 4383.5, 4749.5]}}, "batch_size": 1024, "loss_type": "focal", "pos_weight": 10.0, "use_month_weights": true}