{"network_name": "EPMMOENet", "train_dates": ["20240430"], "test_dates": ["20240531"], "mask_label": "mask_label", "InputGeneral": {"features": ["fellow_follow_decision_maker", "fellow_follow_intention_nio_confirm", "fellow_follow_intention_test_drive", "user_core_first_reg_leads_nio_DSLA", "app_search_intention_cnt_1d", "app_search_intention_cnt_7d"]}, "InputScene": {"features": []}, "InputSeqSet": {"Set": ["main_seq", "car_seq"], "SetInfo": {"main_seq": {"name": "核心-核心行为", "features": ["user_core_action_code_seq", "user_core_action_day_seq"]}, "car_seq": {"name": "和车型相关的核心-核心-核心行为", "features": ["user_car_core_action_code_seq", "user_car_core_action_veh_model_seq", "user_car_core_action_day_seq"]}}}, "RawLabel": {"m_purchase_days_nio_new_car": {}, "d_purchase_days_nio_new_car": {}, "pos_flag": {}}, "RawFeature": {"fellow_follow_decision_maker": {"dtype": "StringLookup", "vocabulary": ["0", "1", "2", "3", "4", "5", "99", "LowFreq", "None"]}, "fellow_follow_intention_nio_confirm": {"dtype": "StringLookup", "vocabulary": ["0", "1", "2", "3", "LowFreq", "None"]}, "fellow_follow_intention_test_drive": {"dtype": "StringLookup", "vocabulary": ["0", "1", "2", "3", "LowFreq", "None"]}, "user_core_first_reg_leads_nio_DSLA": {"dtype": "Bucket", "bin_boundarie": [1.5, 3.5, 5.5, 7.5, 10.5, 13.5, 15.5, 17.5, 20.5, 22.5, 25.5, 27.5, 28.5, 35.5, 47.5, 62.5, 77.5, 87.5, 111.5, 123.5, 143.5, 160.5, 179.5]}, "app_search_intention_cnt_1d": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5]}, "app_search_intention_cnt_7d": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 4.5]}, "user_core_action_code_seq": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 4.5, 5.5, 6.5, 7.5, 8.5, 9.5, 10.5, 11.5, 12.5, 13.5, 14.5, 15.5, 16.5, 17.5, 18.5, 19.5, 20.5, 21.5, 22.5, 23.5, 24.5, 25.5, 26.5, 27.5, 28.5, 29.5, 30.5, 31.5, 32.5, 33.5, 34.5, 35.5, 36.5, 37.5, 38.5, 39.5, 40.5, 41.5, 42.5, 43.5, 44.5, 45.5, 46.5, 47.5, 48.5, 49.5, 50.5, 51.5, 52.5, 53.5, 54.5, 55.5, 56.5, 57.5, 58.5, 59.5, 60.5, 61.5, 62.5, 63.5, 64.5, 65.5, 66.5, 67.5, 68.5, 69.5, 70.5, 71.5, 72.5, 73.5, 74.5, 75.5, 76.5, 77.5, 78.5, 79.5, 80.5, 81.5, 82.5, 83.5, 84.5, 85.5, 86.5, 87.5, 88.5, 89.5, 90.5, 91.5, 92.5, 93.5, 94.5, 95.5, 96.5, 97.5, 98.5, 99.5]}, "user_core_action_day_seq": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 4.5, 5.5, 6.5, 7.5, 8.5, 9.5, 10.5, 11.5, 12.5, 13.5, 14.5, 15.5, 16.5, 17.5, 18.5, 19.5, 20.5, 21.5, 22.5, 23.5, 24.5, 25.5, 26.5, 27.5, 28.5, 29.5, 30.5, 31.5, 32.5, 33.5, 34.5, 35.5, 36.5, 37.5, 38.5, 39.5, 40.5, 41.5, 42.5, 43.5, 44.5, 45.5, 46.5, 47.5, 48.5, 49.5, 50.5, 51.5, 52.5, 53.5, 54.5, 55.5, 56.5, 57.5, 58.5, 59.5, 60.5, 61.5, 62.5, 63.5, 64.5, 65.5, 66.5, 67.5, 68.5, 69.5, 70.5, 71.5, 72.5, 73.5, 74.5, 75.5, 76.5, 77.5, 78.5, 79.5, 80.5, 81.5, 82.5, 83.5, 84.5, 85.5, 86.5, 87.5, 88.5, 89.5, 90.5, 91.5, 92.5, 93.5, 94.5, 95.5, 96.5, 97.5, 98.5, 99.5]}, "user_car_core_action_code_seq": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 4.5, 5.5, 6.5, 7.5, 8.5, 9.5, 10.5, 11.5, 12.5, 13.5, 14.5, 15.5, 16.5, 17.5, 18.5, 19.5, 20.5, 21.5, 22.5, 23.5, 24.5, 25.5, 26.5, 27.5, 28.5, 29.5, 30.5, 31.5, 32.5, 33.5, 34.5, 35.5, 36.5, 37.5, 38.5, 39.5]}, "user_car_core_action_veh_model_seq": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 4.5, 5.5, 6.5, 7.5, 8.5, 9.5, 10.5, 11.5, 12.5, 13.5, 14.5, 15.5, 16.5, 17.5, 18.5, 19.5, 20.5, 21.5, 22.5, 23.5, 24.5, 25.5, 26.5, 27.5, 28.5, 29.5, 30.5, 31.5, 32.5, 33.5, 34.5, 35.5, 36.5, 37.5, 38.5, 39.5]}, "user_car_core_action_day_seq": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 4.5, 5.5, 6.5, 7.5, 8.5, 9.5, 10.5, 11.5, 12.5, 13.5, 14.5, 15.5, 16.5, 17.5, 18.5, 19.5, 20.5, 21.5, 22.5, 23.5, 24.5, 25.5, 26.5, 27.5, 28.5, 29.5, 30.5, 31.5, 32.5, 33.5, 34.5, 35.5, 36.5, 37.5, 38.5, 39.5, 40.5, 41.5, 42.5, 43.5, 44.5, 45.5, 46.5, 47.5, 48.5, 49.5, 50.5, 51.5, 52.5, 53.5, 54.5, 55.5, 56.5, 57.5, 58.5, 59.5, 60.5, 61.5, 62.5, 63.5, 64.5, 65.5, 66.5, 67.5, 68.5, 69.5, 70.5, 71.5, 72.5, 73.5, 74.5, 75.5, 76.5, 77.5, 78.5, 79.5, 80.5, 81.5, 82.5, 83.5, 84.5, 85.5, 86.5, 87.5, 88.5, 89.5, 90.5, 91.5, 92.5, 93.5, 94.5, 95.5, 96.5, 97.5, 98.5, 99.5]}}}