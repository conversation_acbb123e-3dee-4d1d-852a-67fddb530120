{"network_name": "EPMMOENet", "train_dates": ["20240430"], "test_dates": ["20240531"], "mask_label": null, "use_multitask": false, "InputGeneral": {"features": ["user_core_user_gender", "user_core_user_age_group", "user_core_resident_city", "user_core_is_nio_employee", "user_core_pred_career_type", "user_core_nio_user_identity", "user_core_answer_sales_pc_nio_1d_cnt", "user_core_answer_sales_pc_nio_30d_cnt", "user_core_fl_f2f_interview_nio_1d_cnt", "user_core_fl_f2f_interview_nio_30d_cnt", "user_core_fl_offline_im_nio_1d_cnt", "user_core_fl_offline_im_nio_30d_cnt", "user_core_book_td_nio_1d_cnt", "user_core_book_td_nio_30d_cnt", "user_core_exp_td_nio_1d_cnt", "user_core_exp_td_nio_30d_cnt", "user_core_visit_nh_1d_cnt", "user_core_visit_nh_30d_cnt", "user_core_exp_charging_nio_1d_cnt", "user_core_exp_charging_nio_30d_cnt", "user_core_lock_ncar_nio_1d_cnt", "user_core_lock_ncar_nio_30d_cnt", "user_core_pay_ncar_dp_nio_1d_cnt", "user_core_pay_ncar_dp_nio_30d_cnt", "user_core_unfirst_reg_leads_nio_1d_cnt", "user_core_unfirst_reg_leads_nio_30d_cnt", "user_core_save_veh_cgf_nio_1d_cnt", "user_core_save_veh_cgf_nio_30d_cnt", "user_core_del_veh_cgf_nio_1d_cnt", "user_core_del_veh_cgf_nio_30d_cnt", "user_core_view_used_veh_1d_cnt", "user_core_view_used_veh_30d_cnt", "user_core_search_nioapp_1d_cnt", "user_core_search_nioapp_30d_cnt", "user_core_view_cm_hp_nioapp_1d_cnt", "user_core_view_cm_hp_nioapp_30d_cnt", "user_core_view_cm_dp_nioapp_1d_cnt", "user_core_view_cm_dp_nioapp_30d_cnt", "user_core_exp_maintenance_nio_1d_cnt", "user_core_exp_maintenance_nio_30d_cnt", "user_core_checkin_nioapp_1d_cnt", "user_core_checkin_nioapp_30d_cnt", "user_core_buy_cm_nioapp_1d_cnt", "user_core_buy_cm_nioapp_30d_cnt", "user_core_buy_nl_nioapp_1d_cnt", "user_core_buy_nl_nioapp_30d_cnt"]}, "InputScene": {"features": ["user_core_nio_user_identity", "intention_stage", "intention_status", "intention_create_time_days", "user_create_days", "user_register_days"]}, "InputSeqSet": {"Set": [], "SetInfo": {}}, "RawLabel": {"m_purchase_days_nio_new_car": {}, "d_purchase_days_nio_new_car": {}, "pos_flag": {}}, "RawFeature": {"user_core_user_gender": {"dtype": "StringLookup", "vocabulary": ["LowFreq", "None", "女", "男"]}, "user_core_user_age_group": {"dtype": "StringLookup", "vocabulary": ["18-23", "24-30", "31-40", "41-50", "<18", ">50", "LowFreq", "None"]}, "user_core_resident_city": {"dtype": "StringLookup", "vocabulary": ["", "LowFreq", "None", "三亚市", "三明市", "三门峡市", "上海市", "上饶市", "东莞市", "东营市", "中山市", "丰都县", "临汾市", "临沂市", "丹东市", "丽水市", "丽江市", "乌兰察布市", "乌海市", "乌鲁木齐市", "乐山市", "九江市", "云浮市", "五指山市", "亳州市", "伊犁哈萨克自治州", "佛山市", "佳木斯市", "保定市", "保山市", "信阳市", "儋州市", "六安市", "六盘水市", "兰州市", "内江市", "凉山彝族自治州", "包头市", "北京市", "北海市", "十堰市", "南京市", "南充市", "南宁市", "南平市", "南昌市", "南通市", "南阳市", "厦门市", "台州市", "合肥市", "吉安市", "吉林市", "吕梁市", "吴忠市", "周口市", "呼伦贝尔市", "呼和浩特市", "咸宁市", "咸阳市", "哈尔滨市", "唐山市", "商丘市", "商洛市", "喀什地区", "嘉兴市", "四平市", "大同市", "大庆市", "大理白族自治州", "大连市", "天水市", "天津市", "太原市", "威海市", "娄底市", "孝感市", "宁德市", "宁波市", "安庆市", "安康市", "安阳市", "安顺市", "宜宾市", "宜昌市", "宜春市", "宝鸡市", "宣城市", "宿州市", "宿迁市", "岳阳市", "巴中市", "巴彦淖尔市", "常州市", "常德市", "平凉市", "平顶山市", "广元市", "广安市", "广州市", "庆阳市", "廊坊市", "延安市", "延边朝鲜族自治州", "开封市", "张家口市", "张家界市", "张掖市", "徐州市", "德宏傣族景颇族自治州", "德州市", "德阳市", "忻州市", "怀化市", "恩施土家族苗族自治州", "惠州市", "成都市", "扬州市", "承德市", "抚州市", "抚顺市", "拉萨市", "揭阳市", "攀枝花市", "文山壮族苗族自治州", "新乡市", "新余市", "无锡市", "日照市", "昆明市", "昌吉回族自治州", "昭通市", "晋中市", "晋城市", "普洱市", "景德镇市", "曲靖市", "朔州市", "朝阳市", "杭州市", "松原市", "枣庄市", "柳州市", "株洲市", "桂林市", "梅州市", "楚雄彝族自治州", "榆林市", "武威市", "武汉市", "毕节市", "永州市", "汉中市", "汕头市", "汕尾市", "江门市", "池州市", "沈阳市", "沧州市", "河源市", "泉州市", "泰安市", "泰州市", "泸州市", "洛阳市", "济南市", "济宁市", "济源市", "海东市", "海口市", "海外", "淄博市", "淮北市", "淮南市", "淮安市", "深圳市", "清远市", "温州市", "渭南市", "湖州市", "湘潭市", "湘西土家族苗族自治州", "湛江市", "滁州市", "滨州市", "漯河市", "漳州市", "潍坊市", "潮州市", "澄迈县", "澳门区", "濮阳市", "烟台市", "焦作市", "牡丹江市", "玉林市", "玉溪市", "珠海市", "琼海市", "益阳市", "盐城市", "盘锦市", "眉山市", "石嘴山市", "石家庄市", "福州市", "秦皇岛市", "红河哈尼族彝族自治州", "绍兴市", "绥化市", "绵阳市", "聊城市", "肇庆市", "自贡市", "舟山市", "芜湖市", "苏州市", "茂名市", "荆州市", "荆门市", "莆田市", "菏泽市", "萍乡市", "营口市", "葫芦岛市", "蚌埠市", "衡水市", "衡阳市", "衢州市", "襄阳市", "西双版纳傣族自治州", "西宁市", "西安市", "许昌市", "贵港市", "贵阳市", "资阳市", "赣州市", "赤峰市", "辽阳市", "达州市", "运城市", "连云港市", "通化市", "通辽市", "遂宁市", "遵义市", "邢台市", "邯郸市", "邵阳市", "郑州市", "郴州市", "鄂尔多斯市", "重庆市", "金华市", "钦州市", "铁岭市", "铜仁市", "铜川市", "铜陵市", "银川市", "锦州市", "镇江市", "长春市", "长沙市", "长治市", "阜新市", "阜阳市", "阳江市", "阳泉市", "阿克苏地区", "阿勒泰地区", "陵水黎族自治县", "随州市", "雅安市", "青岛市", "鞍山市", "韶关市", "马鞍山市", "驻马店市", "鸡西市", "鹤壁市", "鹰潭市", "黄冈市", "黄山市", "黄石市", "黔东南苗族侗族自治州", "黔南布依族苗族自治州", "黔西南布依族苗族自治州", "齐齐哈尔市", "龙岩市"]}, "user_core_is_nio_employee": {"dtype": "StringLookup", "vocabulary": ["0.0", "1.0", "LowFreq"]}, "user_core_pred_career_type": {"dtype": "StringLookup", "vocabulary": ["LowFreq", "None", "个体经营/服务人员", "互联网从业者", "公务员", "公司职员", "农林牧渔", "制造业", "医务人员", "医生、护士、医药等", "司机", "在校大学生", "大学生", "建筑业", "快递、外卖配送", "教职工", "服务业", "白领", "老师", "金融", "金融从业者", "零售", "非工作人群", "餐饮行业"]}, "user_core_nio_user_identity": {"dtype": "StringLookup", "vocabulary": ["LowFreq", "共同车主", "定金车主", "意向金车主", "正式车主", "粉丝"]}, "user_core_answer_sales_pc_nio_1d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5]}, "user_core_answer_sales_pc_nio_30d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5]}, "user_core_fl_f2f_interview_nio_1d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5]}, "user_core_fl_f2f_interview_nio_30d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5]}, "user_core_fl_offline_im_nio_1d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5]}, "user_core_fl_offline_im_nio_30d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 4.5]}, "user_core_book_td_nio_1d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5]}, "user_core_book_td_nio_30d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5]}, "user_core_exp_td_nio_1d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5]}, "user_core_exp_td_nio_30d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5]}, "user_core_visit_nh_1d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5]}, "user_core_visit_nh_30d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5]}, "user_core_exp_charging_nio_1d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5]}, "user_core_exp_charging_nio_30d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 4.5, 7.5]}, "user_core_lock_ncar_nio_1d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5]}, "user_core_lock_ncar_nio_30d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5]}, "user_core_pay_ncar_dp_nio_1d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5]}, "user_core_pay_ncar_dp_nio_30d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5]}, "user_core_unfirst_reg_leads_nio_1d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5]}, "user_core_unfirst_reg_leads_nio_30d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5]}, "user_core_save_veh_cgf_nio_1d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5]}, "user_core_save_veh_cgf_nio_30d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 5.5, 7.5]}, "user_core_del_veh_cgf_nio_1d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5]}, "user_core_del_veh_cgf_nio_30d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5]}, "user_core_view_used_veh_1d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5]}, "user_core_view_used_veh_30d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 4.5, 7.5]}, "user_core_search_nioapp_1d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 5.5]}, "user_core_search_nioapp_30d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 4.5, 6.5, 8.5, 13.5]}, "user_core_view_cm_hp_nioapp_1d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5]}, "user_core_view_cm_hp_nioapp_30d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 4.5, 6.5, 9.5]}, "user_core_view_cm_dp_nioapp_1d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 4.5, 5.5, 6.5, 8.5, 11.5, 18.5]}, "user_core_view_cm_dp_nioapp_30d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 4.5, 5.5, 7.5, 10.5, 13.5, 18.5, 27.5, 42.5]}, "user_core_exp_maintenance_nio_1d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5]}, "user_core_exp_maintenance_nio_30d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5]}, "user_core_checkin_nioapp_1d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5]}, "user_core_checkin_nioapp_30d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 4.5, 11.5, 20.5, 27.5, 29.5]}, "user_core_buy_cm_nioapp_1d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5]}, "user_core_buy_cm_nioapp_30d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5]}, "user_core_buy_nl_nioapp_1d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5]}, "user_core_buy_nl_nioapp_30d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5]}, "intention_stage": {"dtype": "StringLookup", "vocabulary": ["0.0", "1.0", "LowFreq", "None"]}, "intention_status": {"dtype": "StringLookup", "vocabulary": ["1.0", "2.0", "3.0", "4.0", "LowFreq", "None"]}, "intention_create_time_days": {"dtype": "Bucket", "bin_boundarie": [4.5, 10.5, 16.5, 23.5, 31.5, 39.5, 52.5, 69.5, 83.5, 95.5, 108.5, 129.5, 143.5, 165.5, 179.5]}, "user_create_days": {"dtype": "Bucket", "bin_boundarie": [9.5, 17.5, 31.5, 51.5, 61.5, 73.5, 86.5, 99.5, 110.5, 129.5, 143.5, 156.5, 165.5, 179.5]}, "user_register_days": {"dtype": "Bucket", "bin_boundarie": [4.5, 11.5, 18.5, 28.5, 37.5, 51.5, 66.5, 76.5, 87.5, 97.5, 108.5, 122.5, 135.5, 146.5, 156.5, 165.5, 179.5]}}}