{"network_name": "EPMMOENet", "train_dates": ["20240430"], "test_dates": ["20240531"], "mask_label": null, "use_multitask": false, "InputGeneral": {"features": ["fellow_follow_decision_maker", "fellow_follow_intention_nio_confirm", "fellow_follow_intention_test_drive", "user_core_first_reg_leads_nio_DSLA", "app_search_intention_cnt_1d", "app_search_intention_cnt_7d", "app_search_intention_cnt_14d", "app_search_intention_cnt_30d", "app_search_intention_cnt_60d", "app_search_intention_cnt_90d", "app_search_intention_cnt_180d", "app_search_intention_DSLA", "user_core_unfirst_reg_leads_nio_1d_cnt", "user_core_unfirst_reg_leads_nio_7d_cnt", "user_core_unfirst_reg_leads_nio_30d_cnt", "user_core_unfirst_reg_leads_nio_60d_cnt", "user_core_unfirst_reg_leads_nio_90d_cnt", "user_core_unfirst_reg_leads_nio_180d_cnt", "user_core_unfirst_reg_leads_nio_DSLA", "user_core_user_gender", "user_core_user_age_group", "user_core_resident_city", "user_core_is_nio_employee", "user_core_pred_career_type", "user_core_nio_user_identity", "universe_action_cnt_1d", "universe_action_cnt_7d", "universe_action_cnt_14d", "universe_action_cnt_30d", "user_core_buy_cm_nioapp_1d_cnt", "user_core_buy_cm_nioapp_7d_cnt", "user_core_buy_cm_nioapp_30d_cnt", "user_core_buy_cm_nioapp_60d_cnt", "user_core_buy_cm_nioapp_90d_cnt", "user_core_buy_cm_nioapp_180d_cnt", "user_core_buy_nl_nioapp_1d_cnt", "user_core_buy_nl_nioapp_7d_cnt", "user_core_buy_nl_nioapp_30d_cnt", "user_core_buy_nl_nioapp_60d_cnt", "user_core_buy_nl_nioapp_90d_cnt", "user_core_buy_nl_nioapp_180d_cnt", "user_core_search_nioapp_1d_cnt", "user_core_search_nioapp_7d_cnt", "user_core_search_nioapp_30d_cnt", "user_core_search_nioapp_60d_cnt", "user_core_search_nioapp_90d_cnt", "user_core_search_nioapp_180d_cnt", "user_core_action_cnt_1d", "user_core_action_cnt_7d", "user_core_action_cnt_14d", "user_core_action_cnt_30d", "user_core_action_cnt_60d", "user_core_action_cnt_90d", "user_core_action_cnt_180d", "user_core_book_td_nio_1d_cnt", "user_core_book_td_nio_7d_cnt", "user_core_book_td_nio_30d_cnt", "user_core_book_td_nio_60d_cnt", "user_core_book_td_nio_90d_cnt", "user_core_book_td_nio_180d_cnt", "user_core_book_td_nio_DSLA", "user_core_exp_td_nio_1d_cnt", "user_core_exp_td_nio_7d_cnt", "user_core_exp_td_nio_30d_cnt", "user_core_exp_td_nio_60d_cnt", "user_core_exp_td_nio_90d_cnt", "user_core_exp_td_nio_180d_cnt", "user_core_exp_td_nio_DSLA", "user_core_lock_ncar_nio_1d_cnt", "user_core_lock_ncar_nio_7d_cnt", "user_core_lock_ncar_nio_30d_cnt", "user_core_lock_ncar_nio_60d_cnt", "user_core_lock_ncar_nio_90d_cnt", "user_core_lock_ncar_nio_180d_cnt", "user_core_lock_ncar_nio_DSLA", "user_core_pay_ncar_dp_nio_1d_cnt", "user_core_pay_ncar_dp_nio_7d_cnt", "user_core_pay_ncar_dp_nio_30d_cnt", "user_core_pay_ncar_dp_nio_60d_cnt", "user_core_pay_ncar_dp_nio_90d_cnt", "user_core_pay_ncar_dp_nio_180d_cnt", "user_core_pay_ncar_dp_nio_DSLA", "user_core_view_used_veh_1d_cnt", "user_core_view_used_veh_7d_cnt", "user_core_view_used_veh_30d_cnt", "user_core_view_used_veh_60d_cnt", "user_core_view_used_veh_90d_cnt", "user_core_view_used_veh_180d_cnt", "user_core_view_used_veh_DSLA", "user_core_visit_nh_1d_cnt", "user_core_visit_nh_7d_cnt", "user_core_visit_nh_30d_cnt", "user_core_visit_nh_60d_cnt", "user_core_visit_nh_90d_cnt", "user_core_visit_nh_180d_cnt", "user_core_visit_nh_DSLA", "user_car_core_action_cnt_1d", "user_car_core_action_cnt_7d", "user_car_core_action_cnt_14d", "user_car_core_action_cnt_30d", "user_car_core_action_cnt_60d", "user_car_core_action_cnt_90d", "user_car_core_action_cnt_180d", "universe_action_cnt_60d", "universe_action_cnt_90d", "universe_action_cnt_180d"]}, "InputScene": {"features": ["intention_stage", "intention_status", "intention_create_time_days", "user_create_days", "user_register_days"]}, "InputSeqSet": {"Set": [], "SetInfo": {}}, "RawLabel": {"m_purchase_days_nio_new_car": {}, "d_purchase_days_nio_new_car": {}, "pos_flag": {}}, "RawFeature": {"fellow_follow_decision_maker": {"dtype": "StringLookup", "vocabulary": ["0", "1", "2", "3", "4", "5", "99", "LowFreq", "None"]}, "fellow_follow_intention_nio_confirm": {"dtype": "StringLookup", "vocabulary": ["0", "1", "2", "3", "LowFreq", "None"]}, "fellow_follow_intention_test_drive": {"dtype": "StringLookup", "vocabulary": ["0", "1", "2", "3", "LowFreq", "None"]}, "user_core_first_reg_leads_nio_DSLA": {"dtype": "Bucket", "bin_boundarie": [1.5, 3.5, 5.5, 7.5, 10.5, 13.5, 15.5, 17.5, 20.5, 22.5, 25.5, 27.5, 28.5, 35.5, 47.5, 62.5, 77.5, 87.5, 111.5, 123.5, 143.5, 160.5, 179.5]}, "app_search_intention_cnt_1d": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5]}, "app_search_intention_cnt_7d": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 4.5]}, "app_search_intention_cnt_14d": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 5.5]}, "app_search_intention_cnt_30d": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 4.5, 8.5]}, "app_search_intention_cnt_60d": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 4.5, 9.5]}, "app_search_intention_cnt_90d": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 4.5, 6.5, 11.5]}, "app_search_intention_cnt_180d": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 5.5, 7.5, 12.5]}, "app_search_intention_DSLA": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 3.5, 5.5, 7.5, 10.5, 12.5, 14.5, 16.5, 19.5, 22.5, 25.5, 37.5, 45.5, 56.5, 73.5, 86.5, 114.5, 140.5, 179.5]}, "user_core_unfirst_reg_leads_nio_1d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5]}, "user_core_unfirst_reg_leads_nio_7d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5]}, "user_core_unfirst_reg_leads_nio_30d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5]}, "user_core_unfirst_reg_leads_nio_60d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5]}, "user_core_unfirst_reg_leads_nio_90d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 4.5]}, "user_core_unfirst_reg_leads_nio_180d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 4.5]}, "user_core_unfirst_reg_leads_nio_DSLA": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 4.5, 5.5, 7.5, 10.5, 15.5, 20.5, 30.5, 45.5, 70.5, 100.5, 150.5, 179.5]}, "user_core_user_gender": {"dtype": "StringLookup", "vocabulary": ["M", "F", "Unknown", "None"]}, "user_core_user_age_group": {"dtype": "StringLookup", "vocabulary": ["18-25", "26-30", "31-35", "36-40", "41-45", "46-50", "51+", "Unknown", "None"]}, "user_core_resident_city": {"dtype": "StringLookup", "vocabulary": ["Beijing", "Shanghai", "Guangzhou", "Shenzhen", "Hangzhou", "Chengdu", "Nanjing", "<PERSON><PERSON>", "<PERSON><PERSON>", "Suzhou", "Other", "None"]}, "user_core_is_nio_employee": {"dtype": "StringLookup", "vocabulary": ["0", "1", "None"]}, "user_core_pred_career_type": {"dtype": "StringLookup", "vocabulary": ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "None"]}, "user_core_nio_user_identity": {"dtype": "StringLookup", "vocabulary": ["0", "1", "2", "3", "4", "5", "None"]}, "intention_stage": {"dtype": "StringLookup", "vocabulary": ["A", "I", "D", "O", "None"]}, "intention_status": {"dtype": "StringLookup", "vocabulary": ["Active", "Inactive", "Closed", "None"]}, "intention_create_time_days": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 3.5, 7.5, 15.5, 30.5, 60.5, 90.5, 180.5, 365.5]}, "user_create_days": {"dtype": "Bucket", "bin_boundarie": [0.5, 7.5, 30.5, 90.5, 180.5, 365.5, 730.5, 1095.5]}, "user_register_days": {"dtype": "Bucket", "bin_boundarie": [0.5, 7.5, 30.5, 90.5, 180.5, 365.5, 730.5, 1095.5]}, "universe_action_cnt_1d": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 5.5]}, "universe_action_cnt_7d": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 4.5, 7.5]}, "universe_action_cnt_14d": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 3.5, 5.5, 8.5]}, "universe_action_cnt_30d": {"dtype": "Bucket", "bin_boundarie": [0.5, 2.5, 4.5, 7.5, 12.5]}, "universe_action_cnt_60d": {"dtype": "Bucket", "bin_boundarie": [0.5, 2.5, 5.5, 9.5, 15.5, 25.5]}, "universe_action_cnt_90d": {"dtype": "Bucket", "bin_boundarie": [0.5, 3.5, 6.5, 11.5, 18.5, 30.5]}, "universe_action_cnt_180d": {"dtype": "Bucket", "bin_boundarie": [0.5, 4.5, 8.5, 15.5, 25.5, 40.5]}, "user_core_buy_cm_nioapp_1d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5]}, "user_core_buy_cm_nioapp_7d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5]}, "user_core_buy_cm_nioapp_30d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 4.5]}, "user_core_buy_cm_nioapp_60d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 4.5, 5.5]}, "user_core_buy_cm_nioapp_90d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 4.5, 5.5, 6.5]}, "user_core_buy_cm_nioapp_180d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 4.5, 5.5, 7.5, 9.5]}, "user_core_buy_nl_nioapp_1d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5]}, "user_core_buy_nl_nioapp_7d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5]}, "user_core_buy_nl_nioapp_30d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 4.5]}, "user_core_buy_nl_nioapp_60d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 4.5, 5.5]}, "user_core_buy_nl_nioapp_90d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 4.5, 5.5, 6.5]}, "user_core_buy_nl_nioapp_180d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 4.5, 5.5, 7.5, 9.5]}, "user_core_search_nioapp_1d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5]}, "user_core_search_nioapp_7d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 5.5]}, "user_core_search_nioapp_30d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 5.5, 8.5]}, "user_core_search_nioapp_60d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 5.5, 8.5, 12.5]}, "user_core_search_nioapp_90d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 5.5, 8.5, 12.5, 17.5]}, "user_core_search_nioapp_180d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 5.5, 8.5, 12.5, 17.5, 25.5]}, "user_core_action_cnt_1d": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 5.5]}, "user_core_action_cnt_7d": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 4.5, 7.5, 12.5]}, "user_core_action_cnt_14d": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 3.5, 5.5, 8.5, 15.5]}, "user_core_action_cnt_30d": {"dtype": "Bucket", "bin_boundarie": [0.5, 2.5, 4.5, 7.5, 12.5, 20.5]}, "user_core_action_cnt_60d": {"dtype": "Bucket", "bin_boundarie": [0.5, 2.5, 5.5, 9.5, 15.5, 25.5]}, "user_core_action_cnt_90d": {"dtype": "Bucket", "bin_boundarie": [0.5, 3.5, 6.5, 11.5, 18.5, 30.5]}, "user_core_action_cnt_180d": {"dtype": "Bucket", "bin_boundarie": [0.5, 4.5, 8.5, 15.5, 25.5, 40.5]}, "user_core_book_td_nio_1d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5]}, "user_core_book_td_nio_7d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5]}, "user_core_book_td_nio_30d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 4.5]}, "user_core_book_td_nio_60d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 4.5, 5.5]}, "user_core_book_td_nio_90d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 4.5, 5.5]}, "user_core_book_td_nio_180d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 4.5, 5.5, 7.5]}, "user_core_book_td_nio_DSLA": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 3.5, 7.5, 15.5, 30.5, 60.5, 120.5, 179.5]}, "user_core_exp_td_nio_1d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5]}, "user_core_exp_td_nio_7d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5]}, "user_core_exp_td_nio_30d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 4.5]}, "user_core_exp_td_nio_60d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 4.5, 5.5]}, "user_core_exp_td_nio_90d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 4.5, 5.5]}, "user_core_exp_td_nio_180d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 4.5, 5.5, 7.5]}, "user_core_exp_td_nio_DSLA": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 3.5, 7.5, 15.5, 30.5, 60.5, 120.5, 179.5]}, "user_core_lock_ncar_nio_1d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5]}, "user_core_lock_ncar_nio_7d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5]}, "user_core_lock_ncar_nio_30d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5]}, "user_core_lock_ncar_nio_60d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5]}, "user_core_lock_ncar_nio_90d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5]}, "user_core_lock_ncar_nio_180d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5]}, "user_core_lock_ncar_nio_DSLA": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 7.5, 30.5, 60.5, 179.5]}, "user_core_pay_ncar_dp_nio_1d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5]}, "user_core_pay_ncar_dp_nio_7d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5]}, "user_core_pay_ncar_dp_nio_30d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5]}, "user_core_pay_ncar_dp_nio_60d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5]}, "user_core_pay_ncar_dp_nio_90d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5]}, "user_core_pay_ncar_dp_nio_180d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5]}, "user_core_pay_ncar_dp_nio_DSLA": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 7.5, 30.5, 60.5, 179.5]}, "user_core_view_used_veh_1d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5]}, "user_core_view_used_veh_7d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 5.5]}, "user_core_view_used_veh_30d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 5.5, 8.5]}, "user_core_view_used_veh_60d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 5.5, 8.5, 12.5]}, "user_core_view_used_veh_90d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 5.5, 8.5, 12.5, 17.5]}, "user_core_view_used_veh_180d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 5.5, 8.5, 12.5, 17.5, 25.5]}, "user_core_view_used_veh_DSLA": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 3.5, 7.5, 15.5, 30.5, 60.5, 120.5, 179.5]}, "user_core_visit_nh_1d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5]}, "user_core_visit_nh_7d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5]}, "user_core_visit_nh_30d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 4.5]}, "user_core_visit_nh_60d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 4.5, 5.5]}, "user_core_visit_nh_90d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 4.5, 5.5]}, "user_core_visit_nh_180d_cnt": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 4.5, 5.5, 7.5]}, "user_core_visit_nh_DSLA": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 3.5, 7.5, 15.5, 30.5, 60.5, 120.5, 179.5]}, "user_car_core_action_cnt_1d": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 3.5, 5.5]}, "user_car_core_action_cnt_7d": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 2.5, 4.5, 7.5, 12.5]}, "user_car_core_action_cnt_14d": {"dtype": "Bucket", "bin_boundarie": [0.5, 1.5, 3.5, 5.5, 8.5, 15.5]}, "user_car_core_action_cnt_30d": {"dtype": "Bucket", "bin_boundarie": [0.5, 2.5, 4.5, 7.5, 12.5, 20.5]}, "user_car_core_action_cnt_60d": {"dtype": "Bucket", "bin_boundarie": [0.5, 2.5, 5.5, 9.5, 15.5, 25.5]}, "user_car_core_action_cnt_90d": {"dtype": "Bucket", "bin_boundarie": [0.5, 3.5, 6.5, 11.5, 18.5, 30.5]}, "user_car_core_action_cnt_180d": {"dtype": "Bucket", "bin_boundarie": [0.5, 4.5, 8.5, 15.5, 25.5, 40.5]}}}