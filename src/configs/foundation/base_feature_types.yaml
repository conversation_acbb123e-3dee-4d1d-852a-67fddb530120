# 基础特征类型定义
# 标准化的特征处理方法

feature_type_definitions:
  numerical:
    description: "数值特征，连续型数据"
    default_processing: "normalization"
    alternatives: ["binning", "standardization"]
    
  categorical:
    description: "类别特征，离散型标签"
    default_processing: "string_lookup"
    max_vocab_size: 10000
    min_frequency: 10
    
  sequence:
    description: "序列特征，时间序列或行为序列"
    default_processing: "sequence_embedding"
    default_max_length: 50
    padding: "post"
    
  bucket:
    description: "分箱特征，业务驱动的数值分段"
    default_processing: "bucket_discretization"
    requires_boundaries: true

# 标准化处理参数
processing_defaults:
  missing_value_strategy: "fill_default"
  categorical_missing_token: "<UNK>"
  numerical_missing_value: 0.0
  outlier_threshold: 3.0
  min_category_frequency: 10