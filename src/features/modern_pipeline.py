"""
现代化特征工程管道 - 2025年版本

设计理念：
1. 类型安全：严格的特征类型推断和验证
2. 配置驱动：完全基于配置文件的特征处理
3. 高性能：向量化操作和内存优化
4. 可扩展：模块化设计支持新特征类型
5. 业务语义保持：保留特征工程的业务含义
"""

import pandas as pd
import numpy as np
import tensorflow as tf
from typing import Dict, List, Tuple, Optional, Union, Any
from dataclasses import dataclass, field
from enum import Enum
import logging
from pathlib import Path
import json
import pickle
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)


class FeatureType(Enum):
    """特征类型枚举"""
    NUMERICAL = "numerical"          # 数值特征：直接建模
    CATEGORICAL = "categorical"      # 类别特征：embedding
    BUCKET = "bucket"               # 分桶特征：数值分桶+embedding  
    SEQUENCE = "sequence"           # 序列特征：序列建模
    MULTI_HOT = "multi_hot"         # 多热编码特征
    TIME_SERIES = "time_series"     # 时间序列特征


@dataclass
class FeatureConfig:
    """特征配置类"""
    name: str
    feature_type: FeatureType
    is_required: bool = True
    
    # 数值特征配置
    normalize: bool = True
    clip_range: Optional[Tuple[float, float]] = None
    
    # 类别特征配置
    vocab_size: Optional[int] = None
    embedding_dim: Optional[int] = None
    oov_buckets: int = 1
    
    # 分桶特征配置
    bucket_boundaries: List[float] = field(default_factory=list)
    bucket_embedding_dim: int = 8
    
    # 序列特征配置
    max_sequence_length: int = 50
    sequence_embedding_dim: int = 32
    
    # 缺失值处理
    fill_strategy: str = "mean"  # mean, median, mode, zero, unknown
    missing_indicator: bool = False


@dataclass
class ProcessingStats:
    """特征处理统计信息"""
    feature_name: str
    original_type: str
    processed_type: str
    missing_rate: float
    unique_count: int
    value_range: Optional[Tuple[float, float]] = None
    vocab: Optional[List[str]] = None


class FeatureProcessor(ABC):
    """特征处理器基类"""
    
    @abstractmethod
    def fit(self, data: pd.Series, config: FeatureConfig) -> 'FeatureProcessor':
        """拟合处理器"""
        pass
    
    @abstractmethod
    def transform(self, data: pd.Series) -> Dict[str, tf.Tensor]:
        """转换数据"""
        pass
    
    @abstractmethod
    def get_tf_specs(self) -> Dict[str, tf.TensorSpec]:
        """获取TensorFlow输入规格"""
        pass


class NumericalProcessor(FeatureProcessor):
    """数值特征处理器"""
    
    def __init__(self):
        self.mean_ = None
        self.std_ = None
        self.min_ = None
        self.max_ = None
        self.config = None
        
    def fit(self, data: pd.Series, config: FeatureConfig) -> 'NumericalProcessor':
        self.config = config
        
        # 转换为数值类型
        numeric_data = pd.to_numeric(data, errors='coerce')
        
        # 计算统计量
        self.mean_ = numeric_data.mean()
        self.std_ = numeric_data.std()
        self.min_ = numeric_data.min()
        self.max_ = numeric_data.max()
        
        # 处理clip范围
        if config.clip_range:
            self.min_ = max(self.min_, config.clip_range[0])
            self.max_ = min(self.max_, config.clip_range[1])
            
        logger.info(f"数值特征 {config.name}: mean={self.mean_:.3f}, std={self.std_:.3f}")
        return self
    
    def transform(self, data: pd.Series) -> Dict[str, tf.Tensor]:
        # 转换为数值类型
        numeric_data = pd.to_numeric(data, errors='coerce')
        
        # 填充缺失值
        if self.config.fill_strategy == "mean":
            numeric_data = numeric_data.fillna(self.mean_)
        elif self.config.fill_strategy == "zero":
            numeric_data = numeric_data.fillna(0.0)
        
        # 裁剪数值范围
        if self.config.clip_range:
            numeric_data = numeric_data.clip(*self.config.clip_range)
        
        # 标准化
        if self.config.normalize and self.std_ > 0:
            numeric_data = (numeric_data - self.mean_) / self.std_
            
        result = {
            self.config.name: tf.constant(numeric_data.values, dtype=tf.float32)
        }
        
        # 添加缺失值指示器
        if self.config.missing_indicator:
            missing_mask = pd.to_numeric(data, errors='coerce').isna()
            result[f"{self.config.name}_missing"] = tf.constant(
                missing_mask.astype(np.float32).values, dtype=tf.float32
            )
            
        return result
    
    def get_tf_specs(self) -> Dict[str, tf.TensorSpec]:
        specs = {
            self.config.name: tf.TensorSpec(shape=(), dtype=tf.float32)
        }
        
        if self.config.missing_indicator:
            specs[f"{self.config.name}_missing"] = tf.TensorSpec(shape=(), dtype=tf.float32)
            
        return specs


class CategoricalProcessor(FeatureProcessor):
    """类别特征处理器"""
    
    def __init__(self):
        self.vocab_ = None
        self.vocab_size_ = None
        self.config = None
        
    def fit(self, data: pd.Series, config: FeatureConfig) -> 'CategoricalProcessor':
        self.config = config
        
        # 构建词汇表
        string_data = data.astype(str).fillna("unknown")
        vocab_counts = string_data.value_counts()
        
        # 限制词汇表大小
        if config.vocab_size:
            vocab_counts = vocab_counts.head(config.vocab_size - config.oov_buckets - 1)
        
        self.vocab_ = ["unknown"] + vocab_counts.index.tolist()
        self.vocab_size_ = len(self.vocab_) + config.oov_buckets
        
        # 自动计算embedding维度
        if not config.embedding_dim:
            config.embedding_dim = min(max(4, int(self.vocab_size_ ** 0.25)), 50)
            
        logger.info(f"类别特征 {config.name}: vocab_size={self.vocab_size_}, emb_dim={config.embedding_dim}")
        return self
    
    def transform(self, data: pd.Series) -> Dict[str, tf.Tensor]:
        # 转换为字符串
        string_data = data.astype(str).fillna("unknown")
        
        return {
            self.config.name: tf.constant(string_data.values, dtype=tf.string)
        }
    
    def get_tf_specs(self) -> Dict[str, tf.TensorSpec]:
        return {
            self.config.name: tf.TensorSpec(shape=(), dtype=tf.string)
        }


class BucketProcessor(FeatureProcessor):
    """分桶特征处理器"""
    
    def __init__(self):
        self.boundaries_ = None
        self.config = None
        
    def fit(self, data: pd.Series, config: FeatureConfig) -> 'BucketProcessor':
        self.config = config
        
        # 转换为数值类型
        numeric_data = pd.to_numeric(data, errors='coerce').fillna(0)
        
        # 使用配置的边界或自动计算
        if config.bucket_boundaries:
            self.boundaries_ = sorted(config.bucket_boundaries)
        else:
            # 自动计算分位数边界
            quantiles = [0.1, 0.25, 0.5, 0.75, 0.9]
            self.boundaries_ = numeric_data.quantile(quantiles).tolist()
            
        logger.info(f"分桶特征 {config.name}: boundaries={self.boundaries_}")
        return self
    
    def transform(self, data: pd.Series) -> Dict[str, tf.Tensor]:
        # 转换为数值类型
        numeric_data = pd.to_numeric(data, errors='coerce').fillna(0)
        
        # 分桶
        bucket_ids = np.digitize(numeric_data, self.boundaries_)
        
        return {
            self.config.name: tf.constant(bucket_ids, dtype=tf.int32)
        }
    
    def get_tf_specs(self) -> Dict[str, tf.TensorSpec]:
        return {
            self.config.name: tf.TensorSpec(shape=(), dtype=tf.int32)
        }


class SequenceProcessor(FeatureProcessor):
    """序列特征处理器"""
    
    def __init__(self):
        self.vocab_ = None
        self.vocab_size_ = None
        self.config = None
        
    def fit(self, data: pd.Series, config: FeatureConfig) -> 'SequenceProcessor':
        self.config = config
        
        # 解析序列数据
        sequences = []
        for seq_str in data.fillna(""):
            if isinstance(seq_str, str) and seq_str:
                # 尝试不同的分隔符
                for delimiter in [',', ';', '|', ' ']:
                    if delimiter in seq_str:
                        sequences.extend(seq_str.split(delimiter))
                        break
                else:
                    sequences.append(seq_str)
        
        # 构建词汇表
        vocab_counts = pd.Series(sequences).value_counts()
        self.vocab_ = ["<PAD>", "<UNK>"] + vocab_counts.head(1000).index.tolist()
        self.vocab_size_ = len(self.vocab_)
        
        logger.info(f"序列特征 {config.name}: vocab_size={self.vocab_size_}")
        return self
    
    def transform(self, data: pd.Series) -> Dict[str, tf.Tensor]:
        # 解析序列并转换为ID
        sequences = []
        for seq_str in data.fillna(""):
            if isinstance(seq_str, str) and seq_str:
                # 解析序列
                tokens = []
                for delimiter in [',', ';', '|', ' ']:
                    if delimiter in seq_str:
                        tokens = seq_str.split(delimiter)
                        break
                else:
                    tokens = [seq_str]
                
                # 转换为ID
                token_ids = []
                for token in tokens[:self.config.max_sequence_length]:
                    if token in self.vocab_:
                        token_ids.append(self.vocab_.index(token))
                    else:
                        token_ids.append(1)  # <UNK>
                
                sequences.append(token_ids)
            else:
                sequences.append([])
        
        # 填充序列
        padded_sequences = tf.keras.preprocessing.sequence.pad_sequences(
            sequences, 
            maxlen=self.config.max_sequence_length,
            padding='post',
            value=0  # <PAD>
        )
        
        return {
            self.config.name: tf.constant(padded_sequences, dtype=tf.int32)
        }
    
    def get_tf_specs(self) -> Dict[str, tf.TensorSpec]:
        return {
            self.config.name: tf.TensorSpec(
                shape=(self.config.max_sequence_length,), 
                dtype=tf.int32
            )
        }


class ModernFeaturePipeline:
    """现代化特征工程管道"""
    
    def __init__(self):
        self.processors: Dict[str, FeatureProcessor] = {}
        self.feature_configs: Dict[str, FeatureConfig] = {}
        self.processing_stats: Dict[str, ProcessingStats] = {}
        self.is_fitted = False
        
    def load_config_from_yaml(self, config_path: str) -> 'ModernFeaturePipeline':
        """从YAML配置文件加载特征配置"""
        from src.configs.unified_config_manager import unified_config_manager
        
        config = unified_config_manager.load_experiment_config("sample_20250311_v7-20250311")
        
        for feature_name, feature_obj in config.features.items():
            # 推断特征类型
            feature_type = self._infer_feature_type(feature_name, feature_obj)
            
            # 创建特征配置
            self.feature_configs[feature_name] = FeatureConfig(
                name=feature_name,
                feature_type=feature_type,
                normalize=feature_type == FeatureType.NUMERICAL,
                vocab_size=getattr(feature_obj, 'vocab_size', None),
                bucket_boundaries=getattr(feature_obj, 'boundaries', []),
                fill_strategy=self._get_fill_strategy(feature_name),
                missing_indicator=self._should_add_missing_indicator(feature_name)
            )
            
        logger.info(f"加载了 {len(self.feature_configs)} 个特征配置")
        return self
    
    def _infer_feature_type(self, feature_name: str, feature_obj) -> FeatureType:
        """智能推断特征类型"""
        # 基于配置文件类型
        config_type = getattr(feature_obj, 'type', '')
        
        if config_type == 'Bucket':
            return FeatureType.BUCKET
        elif config_type == 'StringLookup':
            return FeatureType.CATEGORICAL
        
        # 基于特征名称模式
        name_lower = feature_name.lower()
        
        if any(pattern in name_lower for pattern in ['_seq', '_sequence', 'action_code', 'action_day']):
            return FeatureType.SEQUENCE
        elif any(pattern in name_lower for pattern in ['cnt', 'days', 'amount', 'duration']):
            return FeatureType.NUMERICAL
        elif any(pattern in name_lower for pattern in ['type', 'status', 'identity', 'city', 'gender']):
            return FeatureType.CATEGORICAL
        else:
            # 默认为数值类型
            return FeatureType.NUMERICAL
    
    def _get_fill_strategy(self, feature_name: str) -> str:
        """确定缺失值填充策略"""
        name_lower = feature_name.lower()
        
        if 'cnt' in name_lower or 'days' in name_lower:
            return "zero"
        elif any(pattern in name_lower for pattern in ['type', 'status', 'identity']):
            return "mode"
        else:
            return "mean"
    
    def _should_add_missing_indicator(self, feature_name: str) -> bool:
        """判断是否需要添加缺失值指示器"""
        # 对于重要的业务特征添加缺失值指示器
        important_patterns = ['search_intention', 'test_drive', 'purchase']
        return any(pattern in feature_name.lower() for pattern in important_patterns)
    
    def fit(self, data: pd.DataFrame) -> 'ModernFeaturePipeline':
        """拟合特征工程管道"""
        logger.info("开始拟合特征工程管道...")
        
        fitted_count = 0
        
        for feature_name, config in self.feature_configs.items():
            if feature_name not in data.columns:
                logger.warning(f"特征 {feature_name} 在数据中不存在，跳过")
                continue
            
            # 创建对应的处理器
            processor = self._create_processor(config.feature_type)
            
            # 拟合处理器
            try:
                processor.fit(data[feature_name], config)
                self.processors[feature_name] = processor
                
                # 记录统计信息
                self._record_stats(feature_name, data[feature_name], config)
                fitted_count += 1
                
            except Exception as e:
                logger.error(f"特征 {feature_name} 拟合失败: {e}")
                continue
        
        self.is_fitted = True
        logger.info(f"特征工程管道拟合完成，成功处理 {fitted_count}/{len(self.feature_configs)} 个特征")
        return self
    
    def _create_processor(self, feature_type: FeatureType) -> FeatureProcessor:
        """创建特征处理器"""
        if feature_type == FeatureType.NUMERICAL:
            return NumericalProcessor()
        elif feature_type == FeatureType.CATEGORICAL:
            return CategoricalProcessor()
        elif feature_type == FeatureType.BUCKET:
            return BucketProcessor()
        elif feature_type == FeatureType.SEQUENCE:
            return SequenceProcessor()
        else:
            raise ValueError(f"不支持的特征类型: {feature_type}")
    
    def _record_stats(self, feature_name: str, data: pd.Series, config: FeatureConfig):
        """记录特征统计信息"""
        missing_rate = data.isna().mean()
        unique_count = data.nunique()
        
        # 数值范围
        value_range = None
        if config.feature_type in [FeatureType.NUMERICAL, FeatureType.BUCKET]:
            numeric_data = pd.to_numeric(data, errors='coerce')
            if not numeric_data.isna().all():
                value_range = (numeric_data.min(), numeric_data.max())
        
        self.processing_stats[feature_name] = ProcessingStats(
            feature_name=feature_name,
            original_type=str(data.dtype),
            processed_type=config.feature_type.value,
            missing_rate=missing_rate,
            unique_count=unique_count,
            value_range=value_range
        )
    
    def transform(self, data: pd.DataFrame) -> Dict[str, tf.Tensor]:
        """转换数据"""
        if not self.is_fitted:
            raise ValueError("管道尚未拟合，请先调用fit方法")
        
        all_features = {}
        
        for feature_name, processor in self.processors.items():
            if feature_name not in data.columns:
                logger.warning(f"特征 {feature_name} 在转换数据中不存在")
                continue
            
            try:
                feature_tensors = processor.transform(data[feature_name])
                all_features.update(feature_tensors)
            except Exception as e:
                logger.error(f"特征 {feature_name} 转换失败: {e}")
                continue
        
        logger.info(f"成功转换 {len(all_features)} 个特征张量")
        return all_features
    
    def get_model_inputs(self) -> Dict[str, tf.keras.layers.Input]:
        """获取模型输入层定义"""
        inputs = {}
        
        for feature_name, processor in self.processors.items():
            specs = processor.get_tf_specs()
            for tensor_name, spec in specs.items():
                inputs[tensor_name] = tf.keras.layers.Input(
                    shape=spec.shape,
                    dtype=spec.dtype,
                    name=tensor_name
                )
        
        return inputs
    
    def get_feature_embeddings(self, inputs: Dict[str, tf.keras.layers.Input]) -> List[tf.Tensor]:
        """构建特征嵌入层"""
        embeddings = []
        
        for feature_name, processor in self.processors.items():
            config = self.feature_configs[feature_name]
            
            if config.feature_type == FeatureType.NUMERICAL:
                # 数值特征直接使用
                if feature_name in inputs:
                    expanded = tf.keras.layers.Lambda(lambda x: tf.expand_dims(x, -1))(inputs[feature_name])
                    embeddings.append(expanded)
                    
            elif config.feature_type == FeatureType.CATEGORICAL:
                # 类别特征embedding
                if feature_name in inputs:
                    lookup = tf.keras.layers.StringLookup(
                        vocabulary=processor.vocab_,
                        mask_token=None,
                        num_oov_indices=config.oov_buckets
                    )(inputs[feature_name])
                    
                    embedding = tf.keras.layers.Embedding(
                        input_dim=processor.vocab_size_,
                        output_dim=config.embedding_dim,
                        name=f"{feature_name}_embedding"
                    )(lookup)
                    
                    embeddings.append(embedding)
                    
            elif config.feature_type == FeatureType.BUCKET:
                # 分桶特征embedding
                if feature_name in inputs:
                    embedding = tf.keras.layers.Embedding(
                        input_dim=len(processor.boundaries_) + 2,
                        output_dim=config.bucket_embedding_dim,
                        name=f"{feature_name}_bucket_embedding"
                    )(inputs[feature_name])
                    
                    embeddings.append(embedding)
                    
            elif config.feature_type == FeatureType.SEQUENCE:
                # 序列特征处理
                if feature_name in inputs:
                    embedding = tf.keras.layers.Embedding(
                        input_dim=processor.vocab_size_,
                        output_dim=config.sequence_embedding_dim,
                        mask_zero=True,
                        name=f"{feature_name}_seq_embedding"
                    )(inputs[feature_name])
                    
                    # 使用GRU处理序列
                    gru_output = tf.keras.layers.GRU(
                        config.sequence_embedding_dim,
                        name=f"{feature_name}_gru"
                    )(embedding)
                    
                    embeddings.append(gru_output)
        
        return embeddings
    
    def save(self, save_path: str):
        """保存管道"""
        save_data = {
            'feature_configs': {name: config.__dict__ for name, config in self.feature_configs.items()},
            'processing_stats': {name: stats.__dict__ for name, stats in self.processing_stats.items()},
            'is_fitted': self.is_fitted
        }
        
        # 保存处理器
        processors_data = {}
        for name, processor in self.processors.items():
            processors_data[name] = {
                'type': type(processor).__name__,
                'state': processor.__dict__
            }
        save_data['processors'] = processors_data
        
        with open(save_path, 'wb') as f:
            pickle.dump(save_data, f)
        
        logger.info(f"管道已保存到 {save_path}")
    
    def get_summary(self) -> Dict[str, Any]:
        """获取管道摘要"""
        if not self.is_fitted:
            return {"status": "未拟合"}
        
        type_counts = {}
        missing_stats = []
        
        for stats in self.processing_stats.values():
            type_name = stats.processed_type
            type_counts[type_name] = type_counts.get(type_name, 0) + 1
            
            if stats.missing_rate > 0.1:  # 缺失率超过10%
                missing_stats.append({
                    'feature': stats.feature_name,
                    'missing_rate': f"{stats.missing_rate:.1%}",
                    'type': stats.processed_type
                })
        
        return {
            "total_features": len(self.processors),
            "feature_types": type_counts,
            "high_missing_features": sorted(missing_stats, key=lambda x: x['missing_rate'], reverse=True)[:10]
        }