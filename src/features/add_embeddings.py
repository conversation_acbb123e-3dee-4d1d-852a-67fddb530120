import os
import sys
import pickle
import numpy as np
import pandas as pd
import json
from tqdm import tqdm

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

def load_embeddings(embeddings_path):
    """加载用户嵌入向量"""
    print(f"加载嵌入向量: {embeddings_path}")
    with open(embeddings_path, 'rb') as f:
        embeddings = pickle.load(f)
    print(f"加载了 {len(embeddings)} 个用户的嵌入向量")
    # 获取嵌入维度
    sample_key = list(embeddings.keys())[0]
    embedding_dim = embeddings[sample_key].shape[0]
    print(f"嵌入向量维度: {embedding_dim}")
    return embeddings, embedding_dim

def add_embeddings_to_data(data_path, embeddings, output_path, id_column='user_id'):
    """将嵌入向量添加到数据集中"""
    print(f"处理数据: {data_path}")
    # 加载数据
    if data_path.endswith('.parquet'):
        df = pd.read_parquet(data_path)
    else:
        df = pd.read_csv(data_path)
    
    print(f"数据集大小: {len(df)} 行")
    
    # 获取嵌入维度
    sample_key = list(embeddings.keys())[0]
    embedding_dim = embeddings[sample_key].shape[0]
    
    # 创建嵌入特征的空矩阵
    embedding_features = np.zeros((len(df), embedding_dim))
    
    # 统计匹配到嵌入向量的用户数
    matched_count = 0
    
    # 填充嵌入特征
    for i, user_id in enumerate(tqdm(df[id_column].values, desc="添加嵌入特征")):
        if user_id in embeddings:
            embedding_features[i] = embeddings[user_id]
            matched_count += 1
    
    print(f"匹配到嵌入向量的用户数: {matched_count}/{len(df)} ({matched_count/len(df)*100:.2f}%)")
    
    # 创建嵌入特征的列名
    embedding_columns = [f'embedding_{i}' for i in range(embedding_dim)]
    
    # 将嵌入特征添加到数据框
    embedding_df = pd.DataFrame(embedding_features, columns=embedding_columns)
    
    # 合并原始数据和嵌入特征
    result_df = pd.concat([df, embedding_df], axis=1)
    
    # 保存结果
    print(f"保存结果到: {output_path}")
    if output_path.endswith('.parquet'):
        result_df.to_parquet(output_path, index=False)
    else:
        result_df.to_csv(output_path, index=False)
    
    return embedding_columns

def update_model_config(config_path, embedding_columns, output_path=None):
    """更新模型配置，添加嵌入特征定义"""
    print(f"更新模型配置: {config_path}")
    
    # 加载模型配置
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    # 添加嵌入特征定义
    if 'InputEmbedding' not in config:
        config['InputEmbedding'] = {"features": embedding_columns}
    else:
        config['InputEmbedding']['features'] = embedding_columns
    
    # 对每个嵌入特征添加RawFeature定义
    for col in embedding_columns:
        config['RawFeature'][col] = {"dtype": "Dense"}
    
    # 保存更新后的配置
    if output_path is None:
        output_path = config_path.replace('.json', '_with_embeddings.json')
    
    print(f"保存更新后的配置到: {output_path}")
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=4, ensure_ascii=False)
    
    return output_path

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='将嵌入特征添加到模型数据中')
    parser.add_argument('--train_data', type=str, required=True, help='训练数据路径')
    parser.add_argument('--test_data', type=str, required=True, help='测试数据路径')
    parser.add_argument('--eval_data', type=str, help='评估数据路径')
    parser.add_argument('--train_embeddings', type=str, required=True, help='训练集嵌入向量路径')
    parser.add_argument('--test_embeddings', type=str, required=True, help='测试集嵌入向量路径')
    parser.add_argument('--id_column', type=str, default='user_id', help='用户ID列名')
    parser.add_argument('--output_dir', type=str, required=True, help='输出目录')
    parser.add_argument('--model_config', type=str, required=True, help='模型配置文件路径')
    
    args = parser.parse_args()
    
    # 确保输出目录存在
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 加载嵌入向量
    train_embeddings, embedding_dim = load_embeddings(args.train_embeddings)
    test_embeddings, _ = load_embeddings(args.test_embeddings)
    
    # 合并所有嵌入向量以提高匹配率
    all_embeddings = {**train_embeddings, **test_embeddings}
    print(f"合并后的嵌入向量总数: {len(all_embeddings)}")
    
    # 处理训练数据
    train_output = os.path.join(args.output_dir, os.path.basename(args.train_data))
    embedding_columns = add_embeddings_to_data(args.train_data, all_embeddings, train_output, args.id_column)
    
    # 处理测试数据
    test_output = os.path.join(args.output_dir, os.path.basename(args.test_data))
    add_embeddings_to_data(args.test_data, all_embeddings, test_output, args.id_column)
    
    # 处理评估数据（如果提供）
    if args.eval_data:
        eval_output = os.path.join(args.output_dir, os.path.basename(args.eval_data))
        add_embeddings_to_data(args.eval_data, all_embeddings, eval_output, args.id_column)
    
    # 更新模型配置
    config_output = os.path.join(args.output_dir, os.path.basename(args.model_config).replace('.json', '_with_embeddings.json'))
    update_model_config(args.model_config, embedding_columns, config_output)
    
    print("完成!")
    print("新的训练数据:", train_output)
    print("新的测试数据:", test_output)
    if args.eval_data:
        print("新的评估数据:", eval_output)
    print("更新后的模型配置:", config_output)
    print("\n使用以下命令运行带有嵌入特征的模型训练:")
    print(f"python src/train.py --model_code={os.path.basename(config_output).replace('.json', '')} "
          f"--dataset_code=dataset_nio_new_car_v15 "
          f"--evaluate_file=\"{os.path.basename(eval_output if args.eval_data else test_output)}\" "
          f"--data_dir=\"{args.output_dir}\" "
          f"--epochs=50 --patience=10 --batch_size=4096 --run_name=with_embeddings_v1")

if __name__ == "__main__":
    main() 