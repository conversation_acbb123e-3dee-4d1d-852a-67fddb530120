import argparse
import json
import logging
import os
import sys
import glob
import pickle
from pathlib import Path
from datetime import datetime

import numpy as np
import pandas as pd
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers
from tensorflow.keras.callbacks import ModelCheckpoint, EarlyStopping, TensorBoard
from tensorflow.keras.optimizers import Adam, AdamW
from tensorflow.keras.losses import BinaryCrossentropy
from tensorflow.keras.metrics import AUC
from sklearn.preprocessing import StandardScaler, OneHotEncoder
from sklearn.compose import ColumnTransformer
from sklearn.model_selection import train_test_split

# 尝试导入本地模块
try:
    # Ensure all custom objects that might be part of the saved model are imported
    from embedding.embedding_model import (
        build_user_embedding_model, 
        TokenAndPositionEmbedding, 
        TransformerBlock, 
        AttentionPooling,
        MaskCreationLayer, # ADDED MaskCreationLayer
        SeasonalGating,    # ADDED SeasonalGating just in case
        MaskedGlobalAveragePooling1D, # ADDED just in case
        Not<PERSON><PERSON><PERSON><PERSON>ayer, # ADDED just in case
        CastLayer # ADDED just in case
    )
except ImportError:
    # 如果embedding_model模块不在PYTHONPATH中，尝试直接导入
    try:
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        from embedding_model import (
            build_user_embedding_model, 
            TokenAndPositionEmbedding, 
            TransformerBlock, 
            AttentionPooling,
            MaskCreationLayer, # ADDED MaskCreationLayer
            SeasonalGating,    # ADDED SeasonalGating
            MaskedGlobalAveragePooling1D, # ADDED
            NotEqualLayer, # ADDED
            CastLayer # ADDED
        )
    except ImportError as e_inner:
        print(f"无法导入embedding_model模块或其部分组件，请确保该模块可访问。错误: {e_inner}")
        sys.exit(1)

# 数据处理辅助函数 (直接定义，不再尝试从外部模块导入)
def parse_comma_separated_seq(s):
    """将逗号分隔的字符串（或已是列表）转换为列表。如果输入不是字符串或列表，则返回空列表。"""
    if isinstance(s, list):
        return s
    if isinstance(s, str):
        return [token.strip() for token in s.split(',') if token.strip()]
    return []

def safe_parse_list(s): 
    """安全地解析可能为列表或JSON字符串的输入，或者已经是列表的输入。"""
    if isinstance(s, list):
        return s
    if isinstance(s, str): 
        try:
            # 尝试解析为JSON列表 (更健壮，处理带引号的元素等)
            parsed = json.loads(s)
            if isinstance(parsed, list):
                return parsed
            return [] 
        except json.JSONDecodeError:
            return [token.strip() for token in s.split(',') if token.strip()]
        except Exception: 
            return []
    return []

# 自定义Focal Loss实现
class SigmoidFocalCrossEntropy(tf.keras.losses.Loss):
    def __init__(self, alpha=0.25, gamma=2.0, **kwargs):
        super().__init__(**kwargs)
        self.alpha = alpha
        self.gamma = gamma
        
    def call(self, y_true, y_pred):
        y_true = tf.cast(y_true, dtype=tf.float32)
        
        # 确保y_true和y_pred具有相同的形状
        if len(y_true.shape) == 1 and len(y_pred.shape) == 2:
            y_true = tf.expand_dims(y_true, axis=-1)
            
        # 计算BCE损失
        bce = tf.keras.losses.binary_crossentropy(y_true, y_pred, from_logits=False)
        
        # 计算p_t
        p_t = (y_true * y_pred) + ((1 - y_true) * (1 - y_pred))
        
        # 计算alpha_t
        alpha_t = y_true * self.alpha + (1 - y_true) * (1 - self.alpha)
        
        # 应用focal项
        focal_loss = alpha_t * tf.pow(1 - p_t, self.gamma) * bce
        
        return focal_loss
    
    def get_config(self):
        config = super().get_config()
        config.update({
            'alpha': self.alpha,
            'gamma': self.gamma
        })
        return config

# 配置
DATASET_BASE_PATH = Path("data/dataset_nio_new_car_v15")
# OUTPUT_DIR = Path("embedding/training_output") # REMOVED
# OUTPUT_DIR.mkdir(parents=True, exist_ok=True) # REMOVED

# 数据参数
TRAIN_DATE = "20240430"  # 使用4月30日的数据进行训练
TEST_DATE = "20240531"   # 使用5月31日的数据进行测试/预测
VAL_SPLIT = 0.1
MAX_FILES_TO_LOAD = None  # 加载所有文件
LABEL_COL = "m_purchase_days_nio_new_car"
USER_ID_COL = "user_id"

# 序列特征配置
SEQUENCE_FEATURES = {
    "action_seq": "user_core_action_code_seq",
    "day_seq": "user_core_action_day_seq",
}

# 静态特征配置 - 选择关键特征
STATIC_FEATURES_NUM = [
    "user_core_action_cnt_30d", "user_core_action_cnt_60d", "user_core_action_cnt_90d",
    "fellow_follow_30d_cnt", "fellow_follow_60d_cnt", "fellow_follow_90d_cnt",
    "user_core_book_td_nio_30d_cnt", "user_core_book_td_nio_60d_cnt", "user_core_book_td_nio_90d_cnt",
    "user_core_visit_veh_cgf_nio_30d_cnt", "user_core_visit_veh_cgf_nio_60d_cnt",
    "user_core_save_veh_cgf_nio_30d_cnt", "user_core_nio_value"
]

STATIC_FEATURES_CAT = [
    "user_core_user_gender", "user_core_user_age_group", "user_core_resident_city",
    "intention_status", "intention_level", "intention_test_drive_level",
    "user_core_nio_community_identity", "user_core_pred_career_type"
]

# 模型超参数
MAXLEN = 64
EMBED_DIM = 256  
NUM_HEADS = 8    
FF_DIM = 512     
NUM_TRANSFORMER_BLOCKS = 3
MLP_UNITS = [256, 128, 64]
DROPOUT_RATE = 0.2
MLP_DROPOUT = 0.3
FOCAL_GAMMA = 2.0  
FOCAL_ALPHA = 0.75 
BATCH_SIZE = 128
EPOCHS = 20
LEARNING_RATE = 5e-5 
NEG_POS_RATIO = 5    
PATIENCE = 3 

# 特殊token
PAD_TOKEN = "[PAD]"
MASK_TOKEN = "[MASK]"
UNKNOWN_TOKEN = "[UNK]"

# 全局配置
USE_ENHANCED_MODEL = True # 默认使用增强模型
ADDITIONAL_SEQUENCES_CONFIG = {} 
ALL_VOCABS = {} 
INCLUDE_STATIC_FEATURES = True # MODIFIED: Restore to True
LOSS_TYPE = 'focal'
MIN_DF_VOCAB = 5 

# 定义可选的额外序列特征
ALL_AVAILABLE_ADDITIONAL_SEQUENCES = {
    "user_car_actions": "user_car_core_action_code_seq",
    "user_car_models": "user_car_core_action_veh_model_seq",
    "user_car_days": "user_car_core_action_day_seq",
}

def build_vocab(series, min_df=5, max_tokens=None):
    """从Series中构建词汇表"""
    all_tokens = []
    for seq_str in series.dropna():
        all_tokens.extend(parse_comma_separated_seq(seq_str))

    if not all_tokens:
        logging.info("No tokens found for vocab building. Returning default special tokens.")
        return {PAD_TOKEN: 0, MASK_TOKEN: 1, UNKNOWN_TOKEN: 2}, {0: PAD_TOKEN, 1: MASK_TOKEN, 2: UNKNOWN_TOKEN}

    # 统计词频
    token_counts = {}
    for token in all_tokens:
        token_counts[token] = token_counts.get(token, 0) + 1
    
    # 过滤低频token
    filtered_tokens = [token for token, count in token_counts.items() if count >= min_df]
    
    # 限制词汇表大小
    if max_tokens and len(filtered_tokens) > max_tokens:
        filtered_tokens = sorted(filtered_tokens, key=lambda x: token_counts[x], reverse=True)[:max_tokens]
    
    # 创建映射
    token_to_id = {PAD_TOKEN: 0, MASK_TOKEN: 1, UNKNOWN_TOKEN: 2}
    for i, token in enumerate(filtered_tokens):
        token_to_id[token] = i + 3  # 预留0,1,2给特殊token
    
    id_to_token = {idx: token for token, idx in token_to_id.items()}
    logging.info(f"构建词汇表: {len(token_to_id)}个token (包含特殊token), min_df={min_df}")
    return token_to_id, id_to_token

def sequence_to_ids(seq_str, token_to_id, maxlen):
    """将序列字符串转换为ID序列"""
    if pd.isna(seq_str):
        return [token_to_id[PAD_TOKEN]] * maxlen
    
    tokens = parse_comma_separated_seq(seq_str)
    ids = [token_to_id.get(token, token_to_id[UNKNOWN_TOKEN]) for token in tokens]
    
    # 截断或填充
    ids = ids[:maxlen]
    padding_len = maxlen - len(ids)
    ids.extend([token_to_id[PAD_TOKEN]] * padding_len)
    return np.array(ids, dtype=np.int32)

def create_balanced_dataset(df, label_col, neg_pos_ratio=5):
    """创建平衡的数据集"""
    positive_samples = df[df[label_col] == 1]
    negative_samples = df[df[label_col] == 0]
    
    logging.info(f"原始数据：正样本={len(positive_samples)}, 负样本={len(negative_samples)}")
    
    # 如果正样本太少，可以通过过采样增加
    if len(positive_samples) < 1000:
        oversampling_size = min(1000, len(negative_samples)//neg_pos_ratio)
        if oversampling_size > len(positive_samples):
            oversampled_positives = positive_samples.sample(
                n=oversampling_size, 
                replace=True
            )
            positive_samples = pd.concat([positive_samples, oversampled_positives])
    
    # 负样本下采样
    n_neg_samples = min(len(negative_samples), len(positive_samples) * neg_pos_ratio)
    sampled_negatives = negative_samples.sample(n=n_neg_samples)
    
    # 合并正负样本
    balanced_df = pd.concat([positive_samples, sampled_negatives])
    balanced_df = balanced_df.sample(frac=1).reset_index(drop=True)  # 打乱顺序
    
    logging.info(f"平衡后数据：正样本={len(positive_samples)}, 负样本={len(sampled_negatives)}")
    return balanced_df

def preprocess_static_features(df, numerical_features, categorical_features, mode='train', preprocessors=None):
    """
    处理数值型和类别型静态特征。
    - 'train'模式: 拟合预处理器并转换数据，返回处理后的DataFrame、特征名列表和预处理器。
    - 'transform'模式: 使用提供的预处理器转换数据，返回处理后的DataFrame和特征名列表。
    """
    logging.info(f"预处理静态特征 (模式: {mode}, 数值: {len(numerical_features)}个, 类别: {len(categorical_features)}个)")
    
    df_processed = df.copy() # 使用副本
    if preprocessors is None:
        preprocessors = {'num_means': {}, 'num_scalers': {}, 'cat_encoders': {}, 'cat_feature_names': {}}

    processed_feature_names = []
    numerical_cols_to_add = pd.DataFrame(index=df_processed.index) # 新的数值特征列
    categorical_dfs_to_concat = [] # 独热编码结果DataFrame列表
    cols_to_drop_original = [] # 需要删除的原始类别列

    # 处理数值型特征
    available_num_features = [f for f in numerical_features if f in df_processed.columns]
    if available_num_features:
        for feature in available_num_features:
            # 添加缺失指示器
            missing_indicator_col = f"{feature}_is_missing"
            numerical_cols_to_add[missing_indicator_col] = df_processed[feature].isnull().astype(int)
            processed_feature_names.append(missing_indicator_col)

            # 创建临时系列用于处理
            temp_feature_series = df_processed[feature].copy()

            if mode == 'train':
                # 计算并存储均值
                mean_val = temp_feature_series.mean()
                preprocessors['num_means'][feature] = mean_val
                # 填充缺失值
                temp_feature_series = temp_feature_series.fillna(mean_val)
                # 标准化
                scaler = StandardScaler()
                numerical_cols_to_add[feature] = scaler.fit_transform(temp_feature_series.values.reshape(-1, 1)).flatten()
                preprocessors['num_scalers'][feature] = scaler
            elif mode == 'transform':
                # 获取存储的均值
                mean_val = preprocessors['num_means'].get(feature, 0)
                # 填充缺失值
                temp_feature_series = temp_feature_series.fillna(mean_val)
                # 标准化
                if feature in preprocessors['num_scalers']:
                    scaler = preprocessors['num_scalers'][feature]
                    numerical_cols_to_add[feature] = scaler.transform(temp_feature_series.values.reshape(-1, 1)).flatten()
                else:
                    logging.warning(f"预处理器中未找到数值特征 {feature} 的scaler。跳过标准化。")
                    numerical_cols_to_add[feature] = temp_feature_series.fillna(0)
            processed_feature_names.append(feature)

    # 处理类别型特征
    available_cat_features = [f for f in categorical_features if f in df_processed.columns]
    if available_cat_features:
        for feature in available_cat_features:
            # 填充缺失值
            feature_series_for_ohe = df_processed[feature].fillna('Unknown').copy()
            cols_to_drop_original.append(feature)
            
            if mode == 'train':
                # 独热编码
                encoder = OneHotEncoder(sparse_output=False, handle_unknown='ignore')
                encoded_data = encoder.fit_transform(feature_series_for_ohe.values.reshape(-1, 1))
                preprocessors['cat_encoders'][feature] = encoder
                # 获取特征名
                feature_names_onehot = [f"{feature}_{cat_val}" for cat_val in encoder.categories_[0]]
                preprocessors['cat_feature_names'][feature] = feature_names_onehot
            elif mode == 'transform':
                # 独热编码
                if feature in preprocessors['cat_encoders']:
                    encoder = preprocessors['cat_encoders'][feature]
                    encoded_data = encoder.transform(feature_series_for_ohe.values.reshape(-1, 1))
                    feature_names_onehot = preprocessors['cat_feature_names'].get(feature, [])
                    if not feature_names_onehot:
                         logging.warning(f"独热编码特征 {feature} 的类别名不存在。使用默认名称。")
                         feature_names_onehot = [f"{feature}_{i}" for i in range(encoded_data.shape[1])]
                else:
                    logging.warning(f"预处理器中未找到类别特征 {feature} 的编码器。跳过独热编码。")
                    encoded_data = np.zeros((len(df_processed), 0))
                    feature_names_onehot = []
            
            # 添加独热编码结果
            if encoded_data.shape[1] > 0:
                encoded_df = pd.DataFrame(encoded_data, columns=feature_names_onehot, index=df_processed.index)
                categorical_dfs_to_concat.append(encoded_df)
                processed_feature_names.extend(feature_names_onehot)

    # 删除原始数值特征和已处理的类别特征
    original_numerical_to_drop = [f for f in available_num_features if f in df_processed.columns and f in numerical_cols_to_add.columns]
    df_processed = df_processed.drop(columns=original_numerical_to_drop + list(set(cols_to_drop_original)), errors='ignore')

    # 合并所有新特征
    if not numerical_cols_to_add.empty:
        df_processed = pd.concat([df_processed] + [numerical_cols_to_add], axis=1)
    if categorical_dfs_to_concat:
        df_processed = pd.concat([df_processed] + categorical_dfs_to_concat, axis=1)

    if not available_num_features and not available_cat_features:
        logging.warning("没有可用的静态特征进行处理。")
    
    logging.info(f"处理后的静态特征总数: {len(processed_feature_names)}")
    
    return df_processed, processed_feature_names, preprocessors

def create_tf_dataset(df, dataset_info, batch_size, embed_dim, static_features_arr=None, shuffle=True, include_labels=True):
    """创建TF数据集"""
    # 1. 获取序列特征和词汇表
    sequence_features_map = dataset_info["master_sequence_features_map"]
    vocabs = dataset_info["vocabs"]
    
    # 2. 准备序列特征输入
    features_dict = {}
    for seq_name, seq_col in sequence_features_map.items():
        # 转换为数字ID序列
        id_col_name = f"{seq_col}_ids"
        if id_col_name in df.columns:
            # 确保值是numpy数组
            try:
                id_values = np.stack(df[id_col_name].values)
                features_dict[id_col_name] = id_values
            except:
                logging.warning(f"无法将特征 {id_col_name} 转换为张量，跳过")
    
    # 检查是否有成功处理的特征
    if not features_dict:
        logging.error("没有有效的特征可用于创建数据集")
        return None
    
    # 3. 添加静态特征（如果有）
    if static_features_arr is not None:
        features_dict["static_features"] = static_features_arr
    elif "static_features" in dataset_info and dataset_info["static_features"] is not None and len(df) == len(dataset_info["static_features"]):
        # Fallback if static_features_arr is not given, but the one in dataset_info matches the df length
        features_dict["static_features"] = dataset_info["static_features"]
        if shuffle: # A bit of a heuristic: if shuffling, it's likely training data split. Warn if this fallback is used.
            logging.warning("Using static_features from dataset_info as fallback in create_tf_dataset during a shuffle operation. Ensure this is intended.")

    # 4. 准备标签（如果需要）
    target_label_col_name = dataset_info.get("label_col", None)
    
    if include_labels and target_label_col_name and target_label_col_name in df.columns:
        # 确保标签是浮点数
        labels = []
        for label in df[target_label_col_name].values:
            if isinstance(label, str):
                try:
                    # 尝试将字符串解析为列表或字典
                    import json
                    parsed = json.loads(label.replace("'", '"'))
                    # 如果解析为列表且第一个元素为1，则标记为正例
                    if isinstance(parsed, list) and len(parsed) > 0 and parsed[0] == 1:
                        labels.append(1.0)
                    else:
                        labels.append(0.0)
                except:
                    # 解析失败，则为负例
                    labels.append(0.0)
            elif isinstance(label, list):
                # 如果已经是列表且第一个元素为1，则标记为正例
                if len(label) > 0 and label[0] == 1:
                    labels.append(1.0)
                else:
                    labels.append(0.0)
            else:
                # 其他情况，保持原值
                try:
                    labels.append(float(label))
                except:
                    labels.append(0.0)
        
        labels_array = np.array(labels, dtype=np.float32)
    else:
        labels_array = None
    
    # 5. 创建TF数据集
    if labels_array is not None:
        # 为模型输出创建标签字典
        labels_dict = {
            "classifier_output": labels_array,
            # 创建一个假的embedding标签(不会被用于训练，仅满足模型API要求)
            "embedding_output": np.zeros((len(df), embed_dim))
        }
        dataset = tf.data.Dataset.from_tensor_slices((features_dict, labels_dict))
    else:
        dataset = tf.data.Dataset.from_tensor_slices(features_dict)
    
    # 6. 配置数据集
    if shuffle:
        dataset = dataset.shuffle(buffer_size=len(df))
    
    dataset = dataset.batch(batch_size)
    dataset = dataset.prefetch(tf.data.AUTOTUNE)
        
    return dataset

def prepare_dataset(train_df, sequence_features, static_features_num, static_features_cat, label_col):
    """准备训练集，添加标签"""
    df = train_df.copy()
    
    # 检查并处理序列特征
    missing_seq_features = []
    for _, feat_col in sequence_features.items():
        if feat_col not in df.columns:
            missing_seq_features.append(feat_col)
    
    if missing_seq_features:
        logging.warning(f"在数据中缺少以下序列特征: {missing_seq_features}")
    
    # 处理标签
    if label_col in df.columns:
        # 针对购车预测，将m_purchase_days_nio_new_car特殊处理
        target_col = 'target_purchase_next_30d'
        
        # 1. 从原始标签创建是否购车目标
        def process_purchase_days(val):
            if pd.isna(val):
                return 0
            try:
                if isinstance(val, str):
                    val = json.loads(val.replace("'", '"'))
                if isinstance(val, list) and len(val) > 0 and val[0] == 1:
                    return 1
            except:
                pass
            return 0
        
        df[target_col] = df[label_col].apply(process_purchase_days)
        logging.info(f"创建目标标签: {target_col}, 正例数量: {df[target_col].sum()}, 总样本数: {len(df)}")
    else:
        logging.error(f"标签列 {label_col} 不存在")
        return None
    
    return df

def prepare_dataset_for_inference(raw_data, output_dir, training_config, label_col, user_id_col):
    """准备用于推理的数据集
    
    Args:
        raw_data (pd.DataFrame): 原始数据
        output_dir (Path): 输出目录，用于加载模型配置和词汇表
        training_config (dict): 训练配置
        label_col (str): 标签列名
        user_id_col (str): 用户ID列名
        
    Returns:
        dict: 数据集信息
    """
    # 准备基础数据
    df = raw_data.copy()
    
    # 从训练配置获取序列特征映射
    sequence_features_map = training_config.get("master_sequence_features_map", {})
    
    # 加载词汇表
    vocabs = {}
    for seq_name, seq_col in sequence_features_map.items():
        vocab_path = Path(output_dir) / f"vocab_{seq_col}.json"
        if vocab_path.exists():
            try:
                with open(vocab_path, 'r') as f:
                    vocabs[seq_col] = json.load(f)
                logging.info(f"加载序列特征 {seq_col} 的词汇表，大小: {len(vocabs[seq_col])}")
            except Exception as e:
                logging.error(f"加载词汇表 {vocab_path} 时出错: {e}")
    
    # 处理序列ID
    maxlen = training_config.get("maxlen", 64)
    for seq_name, seq_col in sequence_features_map.items():
        if seq_col in df.columns and seq_col in vocabs:
            token_to_id = vocabs[seq_col]
            id_col_name = f"{seq_col}_ids"
            df[id_col_name] = df[seq_col].apply(
                lambda x: sequence_to_ids(x, token_to_id, maxlen)
            )
    
    # 创建数据集信息
    dataset_info = {
        "preprocessed_df": df,
        "master_sequence_features_map": sequence_features_map,
        "label_col": label_col,
        "vocabs": vocabs
    }
    
    # 处理静态特征(如果需要)
    if training_config.get("include_static_features", False):
        # 加载静态特征预处理器
        static_preprocessor_path = Path(output_dir) / "static_preprocessor.pkl"
        if static_preprocessor_path.exists():
            try:
                with open(static_preprocessor_path, 'rb') as f:
                    preprocessors = pickle.load(f)
                
                # 获取静态特征列表
                static_feature_names = training_config.get("static_feature_names", [])
                
                if static_feature_names:
                    # 从配置中推断数值和类别特征
                    num_features = [f for f in static_feature_names if not f.startswith(tuple([cat+"_" for cat in STATIC_FEATURES_CAT]))]
                    cat_features = [f for f in STATIC_FEATURES_CAT if any(feat.startswith(f+"_") for feat in static_feature_names)]
                    
                    # 预处理静态特征
                    df, processed_feature_names, _ = preprocess_static_features(
                        df,
                        num_features,
                        cat_features,
                        mode='transform',
                        preprocessors=preprocessors
                    )
                    
                    # 提取静态特征数组
                    available_features = [f for f in static_feature_names if f in df.columns]
                    if available_features:
                        static_features = np.array(df[available_features].values, dtype=np.float32)
                        dataset_info["static_features"] = static_features
                        dataset_info["static_feature_names"] = available_features
                        logging.info(f"处理了 {len(available_features)} 个静态特征用于推理")
            except Exception as e:
                logging.error(f"加载或应用静态特征预处理器时出错: {e}")
    
    return dataset_info

# 设置命令行参数
def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="NIO用户嵌入模型训练脚本")
    
    parser.add_argument("--train-date", type=str, default=TRAIN_DATE,
                        help="训练数据日期，格式: YYYYMMDD")
    
    parser.add_argument("--test-date", type=str, default=TEST_DATE,
                        help="测试数据日期，格式: YYYYMMDD")
    
    parser.add_argument("--output-dir", type=str, required=True,
                        help="运行的名称，将在 'logs/' 目录下创建子目录来存放所有输出")
    
    parser.add_argument("--dataset-base-path", type=str, default=str(DATASET_BASE_PATH), help="数据集基础路径")
    parser.add_argument("--data-path", type=str, default=None, 
                        help="Direct path or glob pattern to data files (e.g., 'path/to/*.parquet'), overrides date-based loading.")
    parser.add_argument("--user-id-col", type=str, default=USER_ID_COL, help="用户ID列名")
    parser.add_argument("--label-col", type=str, default=LABEL_COL, help="目标标签列名")
    
    parser.add_argument("--max-files", type=int, default=MAX_FILES_TO_LOAD,
                        help="最大加载的文件数量 (用于快速测试)")
    
    # 模型结构参数
    parser.add_argument("--use-enhanced-model", action="store_true", default=USE_ENHANCED_MODEL,
                        help="是否使用增强版embedding模型 (默认: True)")
    parser.add_argument("--include-static-features", action="store_true", default=INCLUDE_STATIC_FEATURES,
                        help="是否包含静态特征 (默认: True)")
    parser.add_argument("--maxlen", type=int, default=MAXLEN,
                        help=f"序列最大长度 (默认: {MAXLEN})")
    parser.add_argument("--embed-dim", type=int, default=EMBED_DIM,
                        help=f"嵌入向量维度 (默认: {EMBED_DIM})")
    parser.add_argument("--num-heads", type=int, default=NUM_HEADS,
                        help=f"Transformer注意力头数量 (默认: {NUM_HEADS})")
    parser.add_argument("--num-transformer-blocks", type=int, default=NUM_TRANSFORMER_BLOCKS,
                        help=f"Transformer块数量 (默认: {NUM_TRANSFORMER_BLOCKS})")
    parser.add_argument("--ff-dim", type=int, default=FF_DIM, 
                        help=f"Transformer前馈网络维度 (默认: {FF_DIM})")
    parser.add_argument("--mlp-units", type=int, nargs="+", default=MLP_UNITS,
                        help=f"MLP层的神经元数量列表 (默认: {MLP_UNITS})")
    parser.add_argument("--dropout-rate", type=float, default=DROPOUT_RATE, 
                        help=f"Transformer层和序列Embedding的Dropout率 (默认: {DROPOUT_RATE})")
    parser.add_argument("--mlp-dropout", type=float, default=MLP_DROPOUT,
                        help=f"MLP层Dropout率 (默认: {MLP_DROPOUT})")

    # 训练控制参数
    parser.add_argument("--batch-size", type=int, default=BATCH_SIZE,
                        help=f"训练批次大小 (默认: {BATCH_SIZE})")
    parser.add_argument("--epochs", type=int, default=EPOCHS,
                        help=f"训练轮数 (默认: {EPOCHS})")
    parser.add_argument("--learning-rate", type=float, default=LEARNING_RATE,
                        help=f"学习率 (默认: {LEARNING_RATE})")
    parser.add_argument("--loss-type", type=str, default=LOSS_TYPE,
                        choices=["focal", "bce"], # Removed weighted_bce as it's not implemented
                        help=f"损失函数类型: focal, bce (默认: {LOSS_TYPE})")
    parser.add_argument("--focal-alpha", type=float, default=FOCAL_ALPHA,
                        help=f"Focal Loss的alpha参数 (默认: {FOCAL_ALPHA})")
    parser.add_argument("--focal-gamma", type=float, default=FOCAL_GAMMA,
                        help=f"Focal Loss的gamma参数 (默认: {FOCAL_GAMMA})")
    parser.add_argument("--patience", type=int, default=PATIENCE, 
                        help=f"早停的耐心值 (默认: {PATIENCE})")
    parser.add_argument("--min-df-vocab", type=int, default=MIN_DF_VOCAB,
                        help=f"构建词汇表时的最小词频 (默认: {MIN_DF_VOCAB})")
    parser.add_argument("--val-split-ratio", type=float, default=VAL_SPLIT, 
                        help=f"验证集比例(如果为0,使用test-date作为验证集) (默认: {VAL_SPLIT})")
    parser.add_argument("--top-k-fractions", nargs="*", type=float, default=[0.001, 0.005, 0.01, 0.05, 0.1], 
                        help="Precision@K和Recall@K的K值分数列表 (用于评估)")
    parser.add_argument("--balance-dataset-neg-pos-ratio", type=int, default=NEG_POS_RATIO, 
                        help=f"是否对训练集进行平衡处理，以及负正样本比例 (0表示不平衡, 默认: {NEG_POS_RATIO})")

    # 运行模式
    parser.add_argument("--mode", type=str, required=True, 
                        choices=["train", "predict", "extract_embedding"],
                        help="操作模式: train, predict, extract_embedding")
    parser.add_argument("--auto-extract", action="store_true", default=True, 
                        help="训练后自动提取训练集和测试集embedding (默认: True)")

    parsed_args = parser.parse_args()

    # REMOVE all global declarations and assignments from here.
    # The script will rely on args.parameter_name or the module-level constants.
    
    return parsed_args

def main(args):
    """主函数，根据模式执行不同的操作"""
    # 设置输出目录 - 改进路径处理逻辑，避免logs嵌套
    if args.output_dir.startswith("logs/"):
        # 如果已经包含logs/前缀，直接使用
        output_dir = Path(args.output_dir)
    else:
        # 否则添加logs/前缀
        output_dir = Path("logs") / args.output_dir
    
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 设置文件日志
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = output_dir / f"{args.mode}_{timestamp}.log"
    file_handler = logging.FileHandler(log_file)
    file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
    logging.getLogger().addHandler(file_handler)
    
    logging.info(f"运行模式: {args.mode}")
    logging.info(f"参数: {args}")
    
    # 根据模式执行操作
    if args.mode == "train":
        train_date = args.train_date
        test_date = args.test_date
        
        # 确定要使用的序列特征
        current_sequence_features = SEQUENCE_FEATURES.copy()
        if args.use_enhanced_model:
            logging.info("使用增强模型，将合并所有可用的附加序列特征。")
            current_sequence_features.update(ALL_AVAILABLE_ADDITIONAL_SEQUENCES)
        logging.info(f"最终使用的序列特征: {current_sequence_features}")

        # 第1步: 加载训练数据
        if args.data_path:
            logging.info(f"使用 --data-path 加载数据: {args.data_path}")
            train_raw_data = load_raw_data(direct_paths=args.data_path, max_files=args.max_files)
        else:
            logging.info(f"加载训练数据 (基于日期): {train_date}")
            dataset_base_path = Path(args.dataset_base_path) if args.dataset_base_path else DATASET_BASE_PATH
            train_raw_data = load_raw_data(
                dataset_base_path=dataset_base_path,
                date_str=train_date,
                max_files=args.max_files
            )
        
        if train_raw_data is None or train_raw_data.empty:
            logging.error(f"无法加载训练数据。")
            return
        
        # 第2步: 准备基础训练数据
        train_df = prepare_dataset(
            train_df=train_raw_data,
            sequence_features=current_sequence_features,
            static_features_num=STATIC_FEATURES_NUM,
            static_features_cat=STATIC_FEATURES_CAT,
            label_col=args.label_col
        )
        
        if train_df is None or train_df.empty:
            logging.error(f"准备基础训练数据失败。")
            return

        # 如果需要，平衡数据集
        if args.balance_dataset_neg_pos_ratio > 0:
            logging.info(f"对训练集进行平衡处理，负正样本比例: {args.balance_dataset_neg_pos_ratio}")
            # Ensure target_col used for balancing is present and correctly named
            target_col_for_balancing = 'target_purchase_next_30d' # This is created in prepare_dataset
            if target_col_for_balancing not in train_df.columns:
                logging.error(f"用于平衡的目标列 '{target_col_for_balancing}' 在train_df中未找到。跳过平衡。")
            else:
                train_df = create_balanced_dataset(train_df, target_col_for_balancing, args.balance_dataset_neg_pos_ratio)
                if train_df.empty:
                    logging.error("平衡数据集后 train_df 为空。中止训练。")
                    return
                logging.info(f"平衡后的训练集大小: {len(train_df)}")

        # 第3步: 建立数据集信息
        train_dataset_info = {
            "preprocessed_df": train_df,
            "master_sequence_features_map": current_sequence_features,
            "label_col": args.label_col
        }
        
        # 第4步: 处理词汇表和序列ID
        vocabs = {}
        for seq_name, seq_col in current_sequence_features.items():
            if seq_col in train_df.columns:
                token_to_id, id_to_token = build_vocab(train_df[seq_col], min_df=args.min_df_vocab)
                vocabs[seq_col] = token_to_id
                logging.info(f"为序列特征 {seq_col} 构建词汇表，大小: {len(token_to_id)}")
                
                id_col_name = f"{seq_col}_ids"
                train_df[id_col_name] = train_df[seq_col].apply(
                    lambda x: sequence_to_ids(x, token_to_id, args.maxlen)
                )
        
        train_dataset_info["vocabs"] = vocabs
        
        # 第5步: 处理静态特征(如果启用)
        if args.include_static_features:
            train_df, static_feature_names, preprocessors = preprocess_static_features(
                train_df, 
                STATIC_FEATURES_NUM, 
                STATIC_FEATURES_CAT,
                mode='train'
            )
            
            if static_feature_names:
                # 提取静态特征数组
                static_features = np.array(train_df[static_feature_names].values, dtype=np.float32)
                train_dataset_info["static_features"] = static_features
                train_dataset_info["static_feature_names"] = static_feature_names
                train_dataset_info["static_preprocessor"] = preprocessors
                logging.info(f"处理了 {len(static_feature_names)} 个静态特征")
        
        # 第6步: 准备验证集
        target_col_for_stratify = 'target_purchase_next_30d' # As defined in prepare_dataset
        if args.val_split_ratio > 0:
            # Check class counts for stratification
            can_stratify = False
            if target_col_for_stratify in train_df.columns:
                class_counts = train_df[target_col_for_stratify].value_counts()
                if len(class_counts) > 1 and class_counts.min() >= 2: # n_splits is 2 for train/test split
                    can_stratify = True
                    logging.info(f"Stratifying by column '{target_col_for_stratify}'. Class counts: {class_counts.to_dict()}")
                else: # This else is for the inner if (len(class_counts) > 1 ...)
                    logging.warning(
                        f"Cannot stratify by '{target_col_for_stratify}' due to insufficient samples in a class "
                        f"(min 2 required). Counts: {class_counts.to_dict()}. Proceeding without stratification."
                    )
            else: # This else is for the outer if (target_col_for_stratify in train_df.columns)
                logging.warning(
                    f"Target column '{target_col_for_stratify}' not found in train_df for stratification. "
                    f"Proceeding without stratification."
                )

            stratify_array = train_df[target_col_for_stratify].values if can_stratify else None
            
            # 分割训练集和验证集
            train_indices, val_indices = train_test_split(
                np.arange(len(train_df)), 
                test_size=args.val_split_ratio,
                random_state=42,
                stratify=stratify_array
            )
            
            train_df_split = train_df.iloc[train_indices].reset_index(drop=True)
            val_df_split = train_df.iloc[val_indices].reset_index(drop=True)
            
            logging.info(f"训练集分割: {len(train_df_split)}行, 验证集分割: {len(val_df_split)}行")

            # Prepare static features for the split dataframes
            train_static_features_split = None
            if "static_features" in train_dataset_info and train_dataset_info["static_features"] is not None:
                if isinstance(train_dataset_info["static_features"], np.ndarray):
                    train_static_features_split = train_dataset_info["static_features"][train_indices]
            else:
                    logging.warning("train_dataset_info[\"static_features\"] is not a numpy array, cannot slice for train split.")

            val_static_features_split = None
            if "static_features" in train_dataset_info and train_dataset_info["static_features"] is not None:
                if isinstance(train_dataset_info["static_features"], np.ndarray):
                    val_static_features_split = train_dataset_info["static_features"][val_indices]
                else:
                    logging.warning("train_dataset_info[\"static_features\"] is not a numpy array, cannot slice for val split.")
            
            # 创建tf.data.Dataset
            train_dataset = create_tf_dataset(
                train_df_split,
                train_dataset_info, # Pass the main info (vocabs, etc.)
                args.batch_size,
                args.embed_dim,
                static_features_arr=train_static_features_split, # Pass sliced static features explicitly
                shuffle=True
            )
            
            val_dataset = create_tf_dataset(
                val_df_split,
                train_dataset_info, # Pass the main info (vocabs, etc.)
                args.batch_size,
                args.embed_dim,
                static_features_arr=val_static_features_split, # Pass sliced static features explicitly
                shuffle=False
            )
            
        else: # This is for if args.val_split_ratio <= 0
            # 使用整个训练集进行训练，使用测试集作为验证
            # For this case, dataset_info["static_features"] should align with train_df if present
            train_dataset = create_tf_dataset(
                train_df, # unsplit train_df
                train_dataset_info,
                args.batch_size,
                args.embed_dim,
                # static_features_arr is not passed, create_tf_dataset will use its fallback if lengths match
                shuffle=True
            )
            
            # 加载测试数据作为验证集
            logging.info(f"加载测试数据作为验证集: {test_date}")
            if args.data_path: # If train data was from data_path, assume test data is also from a similar (but distinct) source or not available this way
                logging.warning("当使用 --data-path 时，测试/验证数据需要单独指定或通过 val_split_ratio 从 --data-path 数据中分割。当前不支持从 --data-path 自动加载测试集。")
                test_raw_data = None # Or handle differently, e.g. expect a --test-data-path
            else:
                dataset_base_path = Path(args.dataset_base_path) if args.dataset_base_path else DATASET_BASE_PATH
                test_raw_data = load_raw_data(
                    dataset_base_path=dataset_base_path,
                    date_str=test_date,
                    max_files=args.max_files
                )
            
            if test_raw_data is None or test_raw_data.empty:
                logging.warning(f"无法加载测试数据: {test_date}，将使用训练数据作为验证集 (注意：这在实际评估中可能导致偏差)")
                # Create a copy of train_dataset for validation to avoid issues with repeated iteration
                # This is not ideal for evaluation but prevents crashing.
                # A better approach if test_raw_data fails is to not have a val_dataset or handle it more gracefully.
                val_dataset = train_dataset # Or consider setting val_dataset = None and handle in model.fit
            else: # This else pairs with 'if test_raw_data is None or test_raw_data.empty:'
                # 准备测试数据
                test_df = prepare_dataset(
                    train_df=test_raw_data, 
                    sequence_features=current_sequence_features,
                    static_features_num=STATIC_FEATURES_NUM,
                    static_features_cat=STATIC_FEATURES_CAT,
                    label_col=args.label_col
                )
                
                # 创建测试数据集info
                # Ensure vocabs from training are used
                test_dataset_info = {
                    "preprocessed_df": test_df,
                    "master_sequence_features_map": current_sequence_features,
                    "label_col": args.label_col,
                    "vocabs": vocabs
                }
                
                # 处理测试集序列特征
                for seq_name, seq_col in current_sequence_features.items():
                    if seq_col in test_df.columns and seq_col in vocabs:
                        token_to_id = vocabs[seq_col]
                        id_col_name = f"{seq_col}_ids"
                        test_df[id_col_name] = test_df[seq_col].apply(
                            lambda x: sequence_to_ids(x, token_to_id, args.maxlen)
                        )
                
                # 处理测试集静态特征(如果有)
                if args.include_static_features and "static_preprocessor" in train_dataset_info:
                    preprocessors = train_dataset_info["static_preprocessor"]
                    test_df, static_feature_names, _ = preprocess_static_features(
                        test_df,
                        STATIC_FEATURES_NUM,
                        STATIC_FEATURES_CAT,
                        mode='transform',
                        preprocessors=preprocessors
                    )
                    
                    # Check if static_feature_names from processing test_df match those from training
                    # This is important for consistency.
                    expected_static_feature_names = train_dataset_info.get("static_feature_names", [])
                    if static_feature_names and all(name in expected_static_feature_names for name in static_feature_names) and len(static_feature_names) == len(expected_static_feature_names):
                        static_features_test_arr = np.array(test_df[static_feature_names].values, dtype=np.float32)
                        test_dataset_info["static_features"] = static_features_test_arr
                        test_dataset_info["static_feature_names"] = static_feature_names
                    elif static_feature_names: # Names do not match or different number of features
                        logging.warning(f"Static feature mismatch between train and test. Train had {len(expected_static_feature_names)} features: {expected_static_feature_names}, Test processing yielded {len(static_feature_names)} features: {static_feature_names}. Proceeding without static features for validation.")
                        test_dataset_info["static_features"] = None # Do not use static features if mismatch
                        test_dataset_info["static_feature_names"] = []
                    else: # No static features processed for test
                        test_dataset_info["static_features"] = None
                        test_dataset_info["static_feature_names"] = []

                val_dataset = create_tf_dataset(
                    test_df, # unsplit test_df for validation
                    test_dataset_info,
                    args.batch_size,
                    args.embed_dim,
                    static_features_arr=test_dataset_info.get("static_features"), # Explicitly pass potentially None static features
                    shuffle=False
                )
        
        # 第7步: 训练模型
        model, extractor = train_model(
            train_dataset_info, 
            train_dataset, 
            val_dataset, 
            args,
            output_dir
        )
        
        # 第8步: 保存训练配置
        save_training_config(
            output_dir=output_dir,
            use_enhanced_model=args.use_enhanced_model,
            include_static_features=args.include_static_features,
            master_sequence_features_map=current_sequence_features,
            maxlen=args.maxlen,
            static_feature_names=train_dataset_info.get("static_feature_names", [])
        )
        
        # 第9步: 保存词汇表
        for feature_name, vocab in train_dataset_info["vocabs"].items():
            vocab_path = output_dir / f"vocab_{feature_name}.json"
            with open(vocab_path, 'w') as f:
                json.dump(vocab, f)
        
        # 第10步: 保存静态特征预处理器(如果有)
        if "static_preprocessor" in train_dataset_info:
            static_preprocessor_path = output_dir / "static_preprocessor.pkl"
            with open(static_preprocessor_path, 'wb') as f:
                pickle.dump(train_dataset_info["static_preprocessor"], f)
        
        # 第11步: 如果启用了自动提取，则为训练集和测试集提取embedding
        if args.auto_extract:
            logging.info("模型训练完成，自动提取embedding...")
            
            # 提取训练集embedding
            train_embeddings = extract_embeddings(
                args=args,
                full_output_dir=output_dir,
                date_str=train_date,
                model=extractor,
                dataset_info=train_dataset_info
            )
            
            # 提取测试集embedding
            if test_date != train_date and 'test_dataset_info' in locals() and test_dataset_info is not None:
                test_embeddings = extract_embeddings(
                    args=args,
                    full_output_dir=output_dir,
                    date_str=test_date,
                    model=extractor,
                    dataset_info=test_dataset_info
                )
    
    elif args.mode == "predict":
        # 加载模型和相关文件
        model, training_config = load_trained_model(output_dir, "predict")
        if model is None:
            return
        
        # Determine sequence features from training_config for consistency
        current_sequence_features = training_config.get("master_sequence_features_map", SEQUENCE_FEATURES)
        if training_config.get("use_enhanced_model", False) and training_config.get("master_sequence_features_map") != ALL_AVAILABLE_ADDITIONAL_SEQUENCES: # Heuristic to add ALL_AVAILABLE if enhanced model was used and map is not already full
            current_sequence_features.update(ALL_AVAILABLE_ADDITIONAL_SEQUENCES)

        if args.data_path:
            logging.info(f"使用 --data-path 加载预测数据: {args.data_path}")
            test_raw_data = load_raw_data(direct_paths=args.data_path, max_files=args.max_files) # Use max_files here too
        else:
            test_date = args.test_date
            dataset_base_path = Path(args.dataset_base_path)
            logging.info(f"加载测试数据: {test_date}")
            test_raw_data = load_raw_data(
                dataset_base_path=dataset_base_path,
                date_str=test_date,
                max_files=args.max_files # Changed from None to args.max_files for consistency
            )
        
        if test_raw_data is None or test_raw_data.empty:
            logging.error(f"无法加载测试数据。")
            return
        
        # 准备测试数据集 (pass current_sequence_features to prepare_dataset_for_inference if it takes it, or ensure it uses training_config correctly)
        test_dataset_info = prepare_dataset_for_inference(
            raw_data=test_raw_data,
            output_dir=output_dir,
            training_config=training_config,
            label_col=args.label_col,
            user_id_col=args.user_id_col
        )
        
        # 创建tf.data.Dataset
        test_dataset = create_tf_dataset(
            test_dataset_info["preprocessed_df"],
            test_dataset_info,
            args.batch_size,
            args.embed_dim,
            shuffle=False
        )
        
        # 进行预测
        predictions = make_predictions(model, test_dataset)
        
        # 合并预测结果和用户ID
        results_df = pd.DataFrame({
            args.user_id_col: test_dataset_info["preprocessed_df"][args.user_id_col],
            "prediction": predictions.flatten()
        })
        
        # 如果有真实标签，计算评估指标
        if args.label_col in test_dataset_info["preprocessed_df"].columns:
            y_true = test_dataset_info["preprocessed_df"][args.label_col].values
            calculate_and_save_metrics(y_true, predictions, args.top_k_fractions, output_dir)
        
        # 保存预测结果
        results_path = output_dir / "predictions.csv"
        results_df.to_csv(results_path, index=False)
        logging.info(f"预测结果已保存到: {results_path}")
    
    elif args.mode == "extract_embedding":
        # 加载提取器模型和相关文件
        extractor, training_config = load_trained_model(output_dir, "extract_embedding")
        if extractor is None:
            return

        current_sequence_features = training_config.get("master_sequence_features_map", SEQUENCE_FEATURES)
        if training_config.get("use_enhanced_model", False) and training_config.get("master_sequence_features_map") != ALL_AVAILABLE_ADDITIONAL_SEQUENCES: # Heuristic
             current_sequence_features.update(ALL_AVAILABLE_ADDITIONAL_SEQUENCES)
        
        data_to_load_date_str = args.test_date # Default to test_date for extraction if no data_path
        if args.data_path:
            logging.info(f"使用 --data-path 加载数据进行embedding提取: {args.data_path}")
            raw_data = load_raw_data(direct_paths=args.data_path, max_files=args.max_files) # Use max_files here too
            data_to_load_date_str = "custom_path" # For naming output file if needed
        else:
            logging.info(f"加载数据 (基于日期): {data_to_load_date_str}")
            dataset_base_path = Path(args.dataset_base_path)
            raw_data = load_raw_data(
                dataset_base_path=dataset_base_path,
                date_str=data_to_load_date_str,
                max_files=args.max_files # Changed from None to args.max_files for consistency
            )
        
        if raw_data is None or raw_data.empty:
            logging.error(f"无法加载数据。")
            return
        
        # 准备数据集
        dataset_info = prepare_dataset_for_inference(
            raw_data=raw_data,
            output_dir=output_dir,
            training_config=training_config,
            label_col=args.label_col,
            user_id_col=args.user_id_col
        )
        
        # 提取embedding
        user_embeddings = extract_embeddings(
            args=args,
            full_output_dir=output_dir,
            date_str=data_to_load_date_str,
            model=extractor,
            dataset_info=dataset_info
        )
    
    else:
        logging.error(f"不支持的模式: {args.mode}")

def extract_embeddings(args, full_output_dir, date_str, model, dataset_info=None):
    """提取指定日期数据的用户embedding"""
    # output_dir = Path(args.output_dir) # REMOVED THIS LINE or similar if it existed
    
    # 如果没有提供dataset_info，需要加载和准备数据
    if dataset_info is None:
        # 加载训练配置
        training_config_path = full_output_dir / "training_config.json" # MODIFIED
        if not training_config_path.exists():
            logging.error(f"找不到训练配置文件: {training_config_path}")
            return None
        
        with open(training_config_path, 'r') as f:
            training_config = json.load(f)
        
        # 加载数据
        data_to_load = args.data_path if args.data_path else date_str # Determine what to load
        
        if args.data_path:
            raw_data = load_raw_data(direct_paths=args.data_path, max_files=args.max_files)
        else:
            dataset_base_path = Path(args.dataset_base_path)
            raw_data = load_raw_data(
                dataset_base_path=dataset_base_path,
                date_str=date_str, # Use original date_str here
                max_files=args.max_files
            )

        if raw_data is None or raw_data.empty:
            logging.error(f"无法加载数据: {data_to_load}") # Use more descriptive name
            return None
        
        # 准备数据集
        dataset_info = prepare_dataset_for_inference(
            raw_data=raw_data,
            output_dir=full_output_dir, # MODIFIED
            training_config=training_config,
            label_col=args.label_col,
            user_id_col=args.user_id_col
        )
    
    # 创建tf.data.Dataset (不带标签，只用于特征提取)
    inference_dataset = create_tf_dataset(
        dataset_info["preprocessed_df"],
        dataset_info,
        args.batch_size,
        args.embed_dim,
        shuffle=False,
        include_labels=False
    )
    
    # 提取embedding
    logging.info(f"为 {len(dataset_info['preprocessed_df'])} 个用户提取embedding...")
    embeddings = []
    
    for batch in inference_dataset:
        batch_embeddings = model.predict(batch, verbose=0)
        embeddings.append(batch_embeddings)
    
    if not embeddings:
        logging.error("无法提取embedding，可能是数据集为空")
        return None
    
    embeddings = np.vstack(embeddings)
    user_ids = dataset_info["preprocessed_df"][args.user_id_col].values
    
    # 保存embedding
    output_path = full_output_dir / f"user_embeddings_{date_str}.pkl" # MODIFIED
    with open(output_path, 'wb') as f:
        pickle.dump({'user_ids': user_ids, 'embeddings': embeddings}, f)
    logging.info(f"用户embedding已保存到: {output_path}")
    
    # 如果是测试日期，同时保存为标准名称(用于检索)
    # Ensure args.test_date is robustly available or passed if needed for this comparison
    if date_str == args.test_date and not args.data_path: # Only save as standard if it's the default test_date and not a custom data_path
        standard_output_path = full_output_dir / "user_embeddings.pkl" # MODIFIED
        with open(standard_output_path, 'wb') as f:
            pickle.dump({'user_ids': user_ids, 'embeddings': embeddings}, f)
        logging.info(f"测试集embedding已同时保存为标准名称: {standard_output_path}")
    
    return {'user_ids': user_ids, 'embeddings': embeddings}

def load_raw_data(dataset_base_path=None, date_str=None, max_files=None, direct_paths=None):
    """加载原始数据
    
    Args:
        dataset_base_path (Path, optional): 数据集基础路径. Required if direct_paths is None.
        date_str (str, optional): 日期字符串(如'20240430'). Required if direct_paths is None.
        max_files (int, optional): 每个日期加载的最大文件数，用于快速测试
        direct_paths (str, optional): 直接的文件路径或glob模式 (e.g., 'path/to/*.parquet' or 'path/to/file.parquet')
        
    Returns:
        pd.DataFrame: 加载的原始数据
    """
    parquet_files = []
    if direct_paths:
        # If direct_paths is a glob pattern
        if "*" in direct_paths or "?" in direct_paths or "[" in direct_paths:
            parquet_files = glob.glob(direct_paths, recursive=True) # Add recursive if subdirs are expected
        else:
            # If direct_paths is a single file path
            parquet_files = [direct_paths]
        logging.info(f"从 direct_paths ({direct_paths}) 找到 {len(parquet_files)} 个 Parquet 文件")
    elif dataset_base_path and date_str:
        target_dir = Path(dataset_base_path) / f"datetime={date_str}"
        if not target_dir.exists():
            logging.error(f"数据目录不存在: {target_dir}")
            return None
        
        # 搜索所有hash_group目录下的parquet文件
        for hash_group_dir in target_dir.glob("hash_group=*"):
            parquet_files.extend(list(hash_group_dir.glob("*.parquet")))
        logging.info(f"在 {target_dir} 找到 {len(parquet_files)} 个 Parquet 文件")
    else:
        logging.error("必须提供 dataset_base_path 和 date_str，或者提供 direct_paths。")
        return None
    
    if not parquet_files:
        logging.error(f"未找到Parquet文件。")
        return None
    
    if max_files and max_files > 0:
        parquet_files = parquet_files[:max_files]
        logging.info(f"限制加载最多 {max_files} 个文件")
    
    # 读取并合并所有dataframe
    dfs = []
    for file_path in parquet_files:
        try:
            df = pd.read_parquet(file_path)
            dfs.append(df)
            logging.info(f"加载文件: {file_path}, 形状: {df.shape}")
        except Exception as e:
            logging.error(f"加载文件 {file_path} 时出错: {e}")
    
    if not dfs:
        logging.error("没有成功加载的文件")
        return None
    
    # 合并所有数据帧
    merged_df = pd.concat(dfs, ignore_index=True)
    logging.info(f"合并后数据形状: {merged_df.shape}")
    
    return merged_df

def train_model(dataset_info, train_dataset, val_dataset, args, output_dir):
    """训练用户embedding模型

    Args:
        dataset_info (dict): 包含数据集信息的字典
        train_dataset (tf.data.Dataset): 训练数据集
        val_dataset (tf.data.Dataset): 验证数据集
        args (argparse.Namespace): 训练参数
        output_dir (Path): 完整的输出目录路径

    Returns:
        tuple: (训练好的模型, embedding提取器)
    """
    logging.info("开始训练用户embedding模型...")
    
    # Get parameters directly from args, as they have defaults defined in parse_args
    embed_dim = args.embed_dim
    maxlen = args.maxlen
    num_heads = args.num_heads
    ff_dim = args.ff_dim
    num_transformer_blocks = args.num_transformer_blocks
    mlp_units = args.mlp_units
    dropout_rate = args.dropout_rate
    mlp_dropout = args.mlp_dropout
    learning_rate = args.learning_rate
    epochs = args.epochs
    # batch_size is not directly used in train_model, but in dataset creation by main
    use_enhanced_model = args.use_enhanced_model
    
    # 使用传入的output_dir，避免重复添加logs/前缀
    current_run_output_dir = output_dir

    # 获取序列特征和词汇表
    sequence_features_map = dataset_info["master_sequence_features_map"]
    vocabs = dataset_info["vocabs"]
    
    # 计算每个序列特征的词汇表大小
    vocab_sizes = {}
    for _, seq_col in sequence_features_map.items():
        if seq_col in vocabs:
            vocab_sizes[seq_col] = len(vocabs[seq_col])
    
    # 构建模型
    model = build_user_embedding_model(
        vocab_sizes=vocab_sizes,
        sequence_names=list(sequence_features_map.values()),
        static_feature_dim=len(dataset_info.get("static_feature_names", [])) if "static_feature_names" in dataset_info else 0,
        embed_dim=embed_dim,
        maxlen=maxlen,
        num_heads=num_heads,
        ff_dim=ff_dim,
        num_transformer_blocks=num_transformer_blocks,
        mlp_units=mlp_units,
        dropout_rate=dropout_rate,
        mlp_dropout=mlp_dropout,
        use_enhanced_model=use_enhanced_model
    )
    
    # 确定分类器损失函数
    classifier_loss = None
    if args.loss_type == 'focal':
        classifier_loss = SigmoidFocalCrossEntropy(
            alpha=args.focal_alpha, 
            gamma=args.focal_gamma
        )
        logging.info(f"Using Focal Loss with alpha={args.focal_alpha}, gamma={args.focal_gamma}")
    elif args.loss_type == 'bce':
        classifier_loss = BinaryCrossentropy(from_logits=False)
        logging.info("Using Binary Cross Entropy loss.")
    else:
        logging.warning(f"Unsupported loss type: {args.loss_type}. Defaulting to Binary Cross Entropy.")
        classifier_loss = BinaryCrossentropy(from_logits=False)

    # 编译模型
    model.compile(
        optimizer=AdamW(learning_rate=learning_rate), # 使用 AdamW 优化器
        loss={
            'classifier_output': classifier_loss, # 使用选择的损失函数
            'embedding_output': 'mse'  # 对embedding输出使用MSE损失(但实际不会优化此输出)
        },
        metrics={
            'classifier_output': AUC(name='auc')
        },
        loss_weights={
            'classifier_output': 1.0,  # 主要优化分类输出
            'embedding_output': 0.0    # 不优化embedding输出
        }
    )
    
    # 创建检查点回调
    checkpoint_callback = ModelCheckpoint(
        str(current_run_output_dir / "model.h5"),
        monitor='val_classifier_output_auc',
        mode='max',
            save_best_only=True,
        verbose=1
    )
    
    # 创建早停回调
    early_stopping = EarlyStopping(
        monitor='val_classifier_output_auc',
        mode='max',
        patience=args.patience,
        verbose=1
    )

    # 创建TensorBoard回调
    tensorboard_log_dir = current_run_output_dir / "tensorboard_logs"
    tensorboard_log_dir.mkdir(parents=True, exist_ok=True)
    tensorboard_callback = TensorBoard(log_dir=str(tensorboard_log_dir), histogram_freq=1)
    
    # 训练模型
    logging.info(f"开始训练，epochs={epochs}, batch_size={args.batch_size}")
    history = model.fit(
        train_dataset,
        validation_data=val_dataset,
        epochs=epochs,
        callbacks=[checkpoint_callback, early_stopping, tensorboard_callback], # ADDED tensorboard_callback
        verbose=1
    )
    
    # 保存训练历史
    history_log_path = current_run_output_dir / "training_history.json"
    try:
        with open(history_log_path, 'w') as f:
            json.dump(history.history, f, indent=4)
        logging.info(f"训练历史已保存到: {history_log_path}")
    except Exception as e:
        logging.error(f"保存训练历史到 {history_log_path} 时出错: {e}")

    # 创建embedding提取器模型
    extractor = tf.keras.Model(
        inputs=model.input,
        outputs=model.get_layer("embedding_output").output
    )
    
    # 保存embedding提取器
    extractor_path = current_run_output_dir / "embedding_extractor.h5"
    extractor.save(extractor_path)
    logging.info(f"Embedding提取器已保存到: {extractor_path}")
    
    # 评估模型性能
    logging.info("模型训练完成，最终验证集性能:")
    evaluation_results = model.evaluate(val_dataset, return_dict=True, verbose=1) # Ensure verbose=1
    logging.info(f"验证集评估结果: {evaluation_results}")

    # Retrieve specific metrics using keys from model.metrics_names or the return_dict keys
    # Assuming 'classifier_output_auc' is the key for AUC and 'classifier_output_loss' for the relevant loss
    val_auc = evaluation_results.get('classifier_output_auc', evaluation_results.get('auc', 0.0)) # Fallback for 'auc' if 'classifier_output_auc' not present
    val_loss = evaluation_results.get('classifier_output_loss', evaluation_results.get('loss', 0.0)) # Fallback for total 'loss'

    logging.info(f"最终验证集 分类器损失: {val_loss:.4f}, 验证集 分类器AUC: {val_auc:.4f}")
    
    return model, extractor

def save_training_config(output_dir, use_enhanced_model, include_static_features, master_sequence_features_map, maxlen, static_feature_names):
    """保存训练配置到JSON文件

    Args:
        output_dir (Path): 输出目录 (This should be the full path logs/run_name)
        use_enhanced_model (bool): 是否使用增强模型
        include_static_features (bool): 是否包含静态特征
        master_sequence_features_map (dict): 序列特征映射字典
        maxlen (int): 序列最大长度
        static_feature_names (list): 静态特征名称列表
    """
    config = {
        "use_enhanced_model": use_enhanced_model,
        "include_static_features": include_static_features,
        "master_sequence_features_map": master_sequence_features_map,
        "maxlen": maxlen,
        "static_feature_names": static_feature_names
    }
    
    config_path = Path(output_dir) / "training_config.json" # Assumes output_dir is already full path
    with open(config_path, 'w') as f:
        json.dump(config, f, indent=4)
    
    logging.info(f"训练配置已保存到: {config_path}")

def load_trained_model(full_output_dir, mode): # MODIFIED: full_output_dir
    """加载已训练的模型和配置
    
    Args:
        full_output_dir (Path): 模型输出目录 (e.g., logs/run_name)
        mode (str): 运行模式，'predict'或'extract_embedding'
        
    Returns:
        tuple: (模型, 训练配置)
    """
    # output_dir = Path(output_dir) # No longer needed if full_output_dir is Path
    
    # 加载训练配置
    config_path = full_output_dir / "training_config.json"
    if not config_path.exists():
        logging.error(f"找不到训练配置文件: {config_path}")
        return None, None
    
    try:
        with open(config_path, 'r') as f:
            training_config = json.load(f)
    except Exception as e:
        logging.error(f"加载训练配置时出错: {e}")
        return None, None

    # Define all custom objects that the models might contain
    custom_objects = {
        "TokenAndPositionEmbedding": TokenAndPositionEmbedding,
        "TransformerBlock": TransformerBlock,
        "AttentionPooling": AttentionPooling,
        "MaskCreationLayer": MaskCreationLayer,
        "SeasonalGating": SeasonalGating,
        "MaskedGlobalAveragePooling1D": MaskedGlobalAveragePooling1D,
        "NotEqualLayer": NotEqualLayer,
        "CastLayer": CastLayer,
        "SigmoidFocalCrossEntropy": SigmoidFocalCrossEntropy # Defined in this script
    }
    
    # 根据模式加载不同的模型
    model_to_load = None
    if mode == 'predict':
        model_path = full_output_dir / "model.h5"
        if not model_path.exists():
            logging.error(f"找不到模型文件: {model_path}")
            return None, None
        try:
            with keras.utils.custom_object_scope(custom_objects):
                model_to_load = tf.keras.models.load_model(str(model_path))
            logging.info(f"已加载预测模型: {model_path}")
            return model_to_load, training_config
        except Exception as e:
            logging.error(f"加载预测模型 {model_path} 时出错: {e}")
            return None, None
    
    elif mode == 'extract_embedding':
        # First, try to load the main model
        model_path = full_output_dir / "model.h5"
        if not model_path.exists():
            logging.error(f"找不到主模型文件: {model_path}，无法构建提取器。")
            return None, None
        try:
            with keras.utils.custom_object_scope(custom_objects):
                full_model = tf.keras.models.load_model(str(model_path), compile=False) # ADDED compile=False
            logging.info(f"已加载主模型: {model_path} 以便创建提取器")
            
            # Now, create the extractor from the loaded full model
            try:
                extractor = tf.keras.Model(
                    inputs=full_model.input,
                    outputs=full_model.get_layer("embedding_output").output
                )
                logging.info(f"已从主模型成功创建Embedding提取器")
                return extractor, training_config
            except Exception as e_extract:
                logging.error(f"从加载的主模型创建提取器时出错: {e_extract}")
                return None, None

        except Exception as e_load_full:
            logging.error(f"加载主模型 {model_path} 时出错: {e_load_full}")
            # Fallback to trying to load the pre-saved extractor if main model fails
            logging.info(f"尝试回退加载预保存的 embedding_extractor.h5")
            extractor_path = full_output_dir / "embedding_extractor.h5"
            if not extractor_path.exists():
                logging.error(f"找不到embedding提取器: {extractor_path}")
                return None, None
            try:
                with keras.utils.custom_object_scope(custom_objects):
                    model_to_load = tf.keras.models.load_model(str(extractor_path))
                logging.info(f"已加载embedding提取器: {extractor_path}")
                return model_to_load, training_config
            except Exception as e_load_extractor:
                logging.error(f"加载预保存的embedding提取器 {extractor_path} 时也出错: {e_load_extractor}")
                return None, None
    
    else:
        logging.error(f"不支持的模式: {mode}")
        return None, None

def make_predictions(model, dataset):
    """使用模型进行预测
    
    Args:
        model (tf.keras.Model): 训练好的模型
        dataset (tf.data.Dataset): 测试数据集
        
    Returns:
        np.ndarray: 预测结果
    """
    logging.info("进行模型预测...")
    predictions = model.predict(dataset)
    
    if isinstance(predictions, list):
        # 如果模型返回多个输出，取第一个
        predictions = predictions[0]
    
    logging.info(f"预测完成，形状: {predictions.shape}")
    return predictions

def calculate_and_save_metrics(y_true, y_pred, top_k_fractions, full_output_dir):
    """计算并保存评估指标
    
    Args:
        y_true (np.ndarray): 真实标签
        y_pred (np.ndarray): 预测分数
        top_k_fractions (list): Precision@K和Recall@K的K值分数列表
        full_output_dir (Path): 输出目录 (e.g., logs/run_name)
    """
    from sklearn.metrics import roc_auc_score, precision_recall_curve, auc
    
    # output_dir = Path(output_dir) # No longer needed if full_output_dir is Path
    metrics = {}
    
    # 计算ROC AUC
    try:
        roc_auc = roc_auc_score(y_true, y_pred)
        metrics["ROC_AUC"] = float(roc_auc)
        logging.info(f"ROC AUC: {roc_auc:.4f}")
    except Exception as e:
        logging.error(f"计算ROC AUC时出错: {e}")
    
    # 计算PR AUC
    try:
        precision, recall, _ = precision_recall_curve(y_true, y_pred)
        pr_auc = auc(recall, precision)
        metrics["PR_AUC"] = float(pr_auc)
        logging.info(f"PR AUC: {pr_auc:.4f}")
    except Exception as e:
        logging.error(f"计算PR AUC时出错: {e}")
    
    # 计算Precision@K和Recall@K
    total_samples = len(y_true)
    total_positives = sum(y_true)
    
    for fraction in top_k_fractions:
        k = max(1, int(total_samples * fraction))
        
        # 获取前K个预测分数的索引
        top_k_indices = np.argsort(y_pred)[-k:]
        
        # 计算Precision@K
        precision_at_k = sum(y_true[i] for i in top_k_indices) / k
        metrics[f"Precision@{k}"] = float(precision_at_k)
        
        # 计算Recall@K
        recall_at_k = sum(y_true[i] for i in top_k_indices) / total_positives if total_positives > 0 else 0
        metrics[f"Recall@{k}"] = float(recall_at_k)
        
        logging.info(f"Top {fraction:.1%} (K={k}): Precision={precision_at_k:.4f}, Recall={recall_at_k:.4f}")
    
    # 保存评估指标
    metrics_path = full_output_dir / "evaluation_metrics.json" # MODIFIED
    with open(metrics_path, 'w') as f:
        json.dump(metrics, f, indent=4)
    
    logging.info(f"评估指标已保存到: {metrics_path}")

if __name__ == "__main__":
    # Use the main parse_args function defined in the script.
    args = parse_args()
    
    try:
        main(args)
    except Exception as e:
        logging.exception(f"运行过程中发生错误: {e}")
        sys.exit(1)