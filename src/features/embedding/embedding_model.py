import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers
import numpy as np
import tensorflow.keras.backend as K
import logging

@tf.keras.utils.register_keras_serializable()
class SqueezeMaskLayer(layers.Layer):
    """自定义层，用于squeeze掩码张量。
    输入形状: (batch_size, 1, 1, maxlen)
    输出形状: (batch_size, maxlen)
    """
    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    def call(self, inputs):
        # Keras序列化Lambda时，有时会丢失对外部模块(如tf)的引用
        # 直接在call方法中使用tf.squeeze可以更稳定
        squeezed_once = tf.squeeze(inputs, axis=1)
        squeezed_twice = tf.squeeze(squeezed_once, axis=1)
        return squeezed_twice

    def get_config(self):
        config = super().get_config()
        return config

@tf.keras.utils.register_keras_serializable()
class TokenAndPositionEmbedding(layers.Layer):
    """实现Token和位置编码层，将序列中的token和位置转换为向量表示"""
    
    def __init__(self, maxlen, vocab_size, embed_dim, **kwargs):
        super().__init__(**kwargs)
        self.maxlen = maxlen
        self.vocab_size = vocab_size
        self.embed_dim = embed_dim
        
        # 创建token嵌入层
        self.token_emb = layers.Embedding(
            input_dim=vocab_size,
            output_dim=embed_dim,
            mask_zero=True,
            name="token_embedding"
        )
        
        # 创建位置嵌入层
        self.pos_emb = layers.Embedding(
            input_dim=maxlen,
            output_dim=embed_dim,
            name="position_embedding"
        )
        
    def call(self, inputs):
        # 生成位置索引
        positions = tf.range(start=0, limit=tf.shape(inputs)[1], delta=1)
        positions = tf.expand_dims(positions, axis=0)
        positions = tf.tile(positions, [tf.shape(inputs)[0], 1])
        
        # 计算token嵌入
        token_embeddings = self.token_emb(inputs)
        
        # 计算位置嵌入
        position_embeddings = self.pos_emb(positions)
        
        # 结合token和位置嵌入
        return token_embeddings + position_embeddings
    
    def get_config(self):
        config = super().get_config()
        config.update({
            "maxlen": self.maxlen,
            "vocab_size": self.vocab_size,
            "embed_dim": self.embed_dim
        })
        return config

@tf.keras.utils.register_keras_serializable()
class TransformerBlock(layers.Layer):
    """Transformer编码器块，使用多头自注意力和前馈神经网络"""
    
    def __init__(self, embed_dim, num_heads, ff_dim, rate=0.1, use_prenorm=True, **kwargs):
        super().__init__(**kwargs)
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.ff_dim = ff_dim
        self.rate = rate
        self.use_prenorm = use_prenorm
        
        # 多头自注意力层
        self.att = layers.MultiHeadAttention(
            num_heads=num_heads, 
            key_dim=embed_dim // num_heads,
            value_dim=embed_dim // num_heads,
            dropout=rate
        )
        
        # 前馈网络
        self.ffn = tf.keras.Sequential([
            layers.Dense(ff_dim, activation="gelu"),
            layers.Dense(embed_dim)
        ])
        
        # Layer normalization
        self.layernorm1 = layers.LayerNormalization(epsilon=1e-6)
        self.layernorm2 = layers.LayerNormalization(epsilon=1e-6)
        
        # Dropout
        self.dropout1 = layers.Dropout(rate)
        self.dropout2 = layers.Dropout(rate)
    
    def call(self, inputs, training=False, mask=None):
        if self.use_prenorm:
            # Pre-norm architecture (更稳定的训练)
            
            # 自注意力部分
            x_norm = self.layernorm1(inputs)
            attention_output = self.att(x_norm, x_norm, x_norm, attention_mask=mask)
            attention_output = self.dropout1(attention_output, training=training)
            out1 = inputs + attention_output
            
            # 前馈网络部分
            x_norm2 = self.layernorm2(out1)
            ffn_output = self.ffn(x_norm2)
            ffn_output = self.dropout2(ffn_output, training=training)
            return out1 + ffn_output
        
        else:
            # Post-norm architecture (传统Transformer)
            attention_output = self.att(inputs, inputs, inputs, attention_mask=mask)
            attention_output = self.dropout1(attention_output, training=training)
            out1 = self.layernorm1(inputs + attention_output)
            
            ffn_output = self.ffn(out1)
            ffn_output = self.dropout2(ffn_output, training=training)
            return self.layernorm2(out1 + ffn_output)
    
    def get_config(self):
        config = super().get_config()
        config.update({
            "embed_dim": self.embed_dim,
            "num_heads": self.num_heads,
            "ff_dim": self.ff_dim,
            "rate": self.rate,
            "use_prenorm": self.use_prenorm
        })
        return config

@tf.keras.utils.register_keras_serializable()
class AttentionPooling(layers.Layer):
    """使用注意力机制对序列特征进行池化，比简单平均池化更有效"""
    
    def __init__(self, embed_dim, num_heads=8, **kwargs):
        super().__init__(**kwargs)
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        
        # 多头自注意力层
        self.multihead_attn = layers.MultiHeadAttention(
            num_heads=num_heads, 
            key_dim=embed_dim // num_heads
        )
        
        # 查询向量(可学习参数)
        self.query = tf.Variable(
            initial_value=tf.random.normal((1, 1, embed_dim)),
            trainable=True,
            name="pooling_query"
        )
    
    def call(self, inputs, mask=None):
        # 扩展查询向量以匹配批次大小
        batch_size = tf.shape(inputs)[0]
        query = tf.tile(self.query, [batch_size, 1, 1])
        
        actual_attention_mask = None
        if mask is not None:
            # MultiHeadAttention expects a mask of shape (B, Tq, Tv) or one that can broadcast to it.
            # Query shape (B, 1, D) -> Tq = 1
            # Inputs shape (B, M, D) -> Tv = M (maxlen)
            # Original squeezed_mask is (B, M). We expand it to (B, 1, M).
            actual_attention_mask = tf.expand_dims(mask, axis=1)
            
        # 应用多头注意力
        pooled = self.multihead_attn(query, inputs, inputs, attention_mask=actual_attention_mask)
        
        # 将结果压缩为[batch_size, embed_dim]
        # pooled should have a shape of (batch_size, 1, embed_dim)
        return tf.squeeze(pooled, axis=1)
    
    def get_config(self):
        config = super().get_config()
        config.update({
            "embed_dim": self.embed_dim,
            "num_heads": self.num_heads
        })
        return config

@tf.keras.utils.register_keras_serializable()
class SeasonalGating(layers.Layer):
    """季节性门控机制：根据时间信息动态调整特征重要性"""
    def __init__(self, embed_dim, activation='sigmoid', **kwargs):
        super().__init__(**kwargs)
        self.embed_dim = embed_dim
        self.activation = activation
        self.day_gate = layers.Dense(embed_dim, activation=activation)
        
    def call(self, sequence_features, day_features):
        # day_features: 编码日期信息的特征
        # sequence_features: 需要调整重要性的序列特征
        
        # 全局池化提取时间模式
        day_pooled = tf.reduce_mean(day_features, axis=1)  # (batch_size, embed_dim)
        
        # 生成门控系数
        gate_values = self.day_gate(day_pooled)  # (batch_size, embed_dim)
        
        # 扩展门控维度以匹配序列特征
        gate_values = tf.expand_dims(gate_values, axis=1)  # (batch_size, 1, embed_dim)
        
        # 应用门控
        gated_features = sequence_features * gate_values
        
        return gated_features
    
    def get_config(self):
        config = super().get_config()
        config.update({
            "embed_dim": self.embed_dim,
            "activation": self.activation
        })
        return config

@tf.keras.utils.register_keras_serializable()
class MaskCreationLayer(tf.keras.layers.Layer):
    """创建掩码层
    
    将输入张量中不等于0的元素标记为1.0，等于0的元素标记为0.0
    并将结果扩展为注意力掩码所需的形状([batch_size, 1, 1, seq_len])
    """
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
    
    def build(self, input_shape):
        # 无需学习的参数
        pass
    
    def call(self, inputs):
        # 创建布尔掩码：inputs != 0
        bool_mask = tf.math.not_equal(inputs, 0)
        # 将布尔掩码转换为浮点掩码
        float_mask = tf.cast(bool_mask, tf.float32)
        # 添加两个维度以匹配注意力掩码的形状要求
        expanded_mask1 = tf.expand_dims(float_mask, axis=1)
        expanded_mask2 = tf.expand_dims(expanded_mask1, axis=1)
        return expanded_mask2

    def get_config(self):
        config = super().get_config()
        return config

@tf.keras.utils.register_keras_serializable()
class MaskedGlobalAveragePooling1D(tf.keras.layers.Layer):
    """带掩码的全局平均池化层"""
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
    
    def call(self, inputs, mask=None):
        if mask is None:
            return tf.reduce_mean(inputs, axis=1)
        
        # 将布尔掩码转换为浮点掩码
        float_mask = tf.cast(mask, tf.float32)
        # 扩展维度以匹配输入形状
        float_mask = tf.expand_dims(float_mask, axis=-1)
        
        # 掩码后的和
        masked_sum = tf.reduce_sum(inputs * float_mask, axis=1)
        # 掩码中的有效元素数量
        valid_elements = tf.reduce_sum(float_mask, axis=1)
        # 防止除以零
        valid_elements = tf.maximum(valid_elements, 1.0)
        
        # 计算掩码元素的平均值
        return masked_sum / valid_elements

    def get_config(self):
        config = super().get_config()
        return config

@tf.keras.utils.register_keras_serializable()
class NotEqualLayer(tf.keras.layers.Layer):
    """不等于层
    
    将输入张量中不等于指定值的元素标记为True
    """
    def __init__(self, value=0, **kwargs):
        super().__init__(**kwargs)
        self.value = value
    
    def call(self, inputs):
        return tf.math.not_equal(inputs, self.value)
    
    def get_config(self):
        config = super().get_config()
        config.update({"value": self.value})
        return config

@tf.keras.utils.register_keras_serializable()
class CastLayer(tf.keras.layers.Layer):
    """类型转换层
    
    将输入张量转换为指定的数据类型
    """
    def __init__(self, dtype=tf.float32, **kwargs):
        super().__init__(**kwargs)
        self.output_dtype = dtype
    
    def call(self, inputs):
        return tf.cast(inputs, self.output_dtype)
    
    def get_config(self):
        config = super().get_config()
        config.update({"dtype": self.output_dtype})
        return config

def build_user_embedding_model(
    vocab_sizes,
    sequence_names,
    static_feature_dim=0,
    embed_dim=256,
    num_heads=8,
    ff_dim=512,
    num_transformer_blocks=3,
    maxlen=64,
    mlp_units=[256, 128, 64],
    dropout_rate=0.2,
    mlp_dropout=0.3,
    use_enhanced_model=True # This arg will be temporarily overridden for fusion logic
):
    """构建用户embedding模型 (恢复静态特征处理)
    
    Args:
        vocab_sizes (dict): 每个序列特征的词汇表大小字典 {feature_name: vocab_size}
        sequence_names (list): 序列特征名称列表
        static_feature_dim (int): 静态特征维度
        embed_dim (int): embedding维度
        num_heads (int): Transformer注意力头数
        ff_dim (int): Transformer前馈网络维度
        num_transformer_blocks (int): Transformer块的数量
        maxlen (int): 序列最大长度
        mlp_units (list): MLP层的神经元数量列表
        dropout_rate (float): 序列特征Dropout率
        mlp_dropout (float): MLP层Dropout率
        use_enhanced_model (bool): 是否使用增强版模型
        
    Returns:
        tf.keras.Model: 构建的用户embedding模型
    """
    # 1. 定义输入层
    inputs_dict = {}
    
    # 序列输入
    sequence_outputs = []
    for seq_name in sequence_names:
        if seq_name in vocab_sizes:
            # 创建输入层
            id_col_name = f"{seq_name}_ids"
            inputs_dict[id_col_name] = layers.Input(shape=(maxlen,), name=id_col_name)
            
            # 应用token和位置编码
            x = TokenAndPositionEmbedding(
                maxlen=maxlen,
                vocab_size=vocab_sizes[seq_name],
                embed_dim=embed_dim,
                name=f"{seq_name}_token_position_embedding"
            )(inputs_dict[id_col_name])
            
            # 创建掩码层
            mask_layer_instance = MaskCreationLayer(name=f"{seq_name}_mask_layer")
            
            # 应用多层Transformer编码器
            for i in range(num_transformer_blocks):
                # 创建注意力掩码，处理填充token
                mask = mask_layer_instance(inputs_dict[id_col_name])
                
                x = TransformerBlock(
                    embed_dim=embed_dim,
                    num_heads=num_heads,
                    ff_dim=ff_dim,
                    rate=dropout_rate,
                    use_prenorm=use_enhanced_model,
                    name=f"{seq_name}_transformer_block_{i+1}"
                )(x, mask=mask)
            
            # 对序列特征进行池化
            if use_enhanced_model:
                # 使用注意力池化
                squeezed_mask = SqueezeMaskLayer(name=f"{seq_name}_squeeze_mask_layer")(mask)
                
                sequence_output = AttentionPooling(
                    embed_dim=embed_dim,
                    num_heads=num_heads,
                    name=f"{seq_name}_attention_pooling"
                )(x, mask=squeezed_mask)
            else:
                # 使用自定义的全局平均池化
                not_equal_layer = NotEqualLayer(value=0, name=f"{seq_name}_not_equal")
                bool_mask = not_equal_layer(inputs_dict[id_col_name])
                cast_layer = CastLayer(dtype=tf.float32, name=f"{seq_name}_cast")
                float_mask = cast_layer(bool_mask)
                sequence_output = MaskedGlobalAveragePooling1D(
                    name=f"{seq_name}_average_pooling"
                )(x, mask=float_mask)
            
            sequence_outputs.append(sequence_output)
    
    # 2. 合并序列特征
    if len(sequence_outputs) > 1:
        concat_sequences = layers.Concatenate(name="sequence_concat")(sequence_outputs)
        if use_enhanced_model:
            gate = layers.Dense(len(sequence_outputs) * embed_dim, activation="sigmoid", name="sequence_gate")(concat_sequences)
            gated_sequences = layers.Multiply(name="gated_sequences")([concat_sequences, gate])
            sequence_features = layers.Dense(embed_dim, name="sequence_projection")(gated_sequences)
        else:
            sequence_features = layers.Dense(embed_dim, name="sequence_projection")(concat_sequences)
    elif len(sequence_outputs) == 1:
        sequence_features = sequence_outputs[0]
    else:
        # 如果没有序列特征，创建一个零向量占位符以避免后续 MLP 出错
        # 这在实践中不应该发生，因为模型至少需要一个输入
        logging.warning("没有有效的序列特征输入到模型。")
        # 创建一个零张量作为 merged_features。需要知道批次大小，但模型构建时不知道。
        # 因此，如果真的没有序列，模型可能会出错。
        # 一个更健壮的做法是确保至少有一个输入。
        # 或者，如果允许无序列输入，MLP部分需要能处理None输入。
        # 为简单起见，假设总会有序列输入。
        if not inputs_dict: # Should not happen if sequence_names is not empty
             # Fallback: create a dummy input if all sequence processing failed.
             # This is a defensive measure; ideally, an error should be raised earlier.
             dummy_input_name = "dummy_input_for_no_sequences"
             inputs_dict[dummy_input_name] = layers.Input(shape=(1,), name=dummy_input_name) # Arbitrary shape
             sequence_features = layers.Dense(embed_dim, name="dummy_sequence_features")(inputs_dict[dummy_input_name])

    # RESTORED: Static feature processing
    if static_feature_dim > 0:
        static_inputs = layers.Input(shape=(static_feature_dim,), name="static_features")
        inputs_dict["static_features"] = static_inputs
        
        processed_static_features = layers.Dense(embed_dim, activation="relu", name="static_hidden")(static_inputs)
        processed_static_features = layers.BatchNormalization(name="static_batchnorm")(processed_static_features)
        processed_static_features = layers.Dropout(mlp_dropout, name="static_dropout")(processed_static_features)
        
        if sequence_features is not None:
            # Current working solution: simple concatenation for static and sequence feature fusion.
            merged_features = layers.Concatenate(name="feature_concat_forced")([
                sequence_features, processed_static_features
            ])
            merged_features = layers.Dense(embed_dim, activation="relu", name="projection_after_forced_concat")(merged_features)
            
        elif processed_static_features is not None: # Only static features available
            merged_features = processed_static_features
        else: # Should not happen if static_feature_dim > 0
             merged_features = None # Or handle error

    elif sequence_features is not None: # Only sequence features available
        merged_features = sequence_features
    else: # No features available at all
        raise ValueError("Neither sequence nor static features are available to build the model.")

    if merged_features is None:
        raise ValueError("merged_features is None before MLP layers. Model cannot be built.")

    # 4. MLP层，用于预测任务
    x = merged_features 
    
    # 添加最终的MLP层
    for i, units in enumerate(mlp_units):
        x = layers.Dense(units, activation="relu", name=f"mlp_layer_{i+1}")(x)
        # Note: Original code had BatchNormalization only if use_enhanced_model was True for MLP part.
        # For consistency in this debug step, let's decide whether to include it or not.
        # Let's keep it as it was, respecting use_enhanced_model for the MLP part for now.
        if use_enhanced_model: # This use_enhanced_model still governs MLP structure
            x = layers.BatchNormalization(name=f"batchnorm_{i+1}")(x)
        x = layers.Dropout(mlp_dropout, name=f"mlp_dropout_{i+1}")(x)
    
    # 5. 输出层
    outputs = [
        layers.Dense(1, activation="sigmoid", name="classifier_output")(x),
        layers.Identity(name="embedding_output")(merged_features) 
    ]
    
    # 6. 构建模型
    # Ensure inputs_dict is not empty before creating the model
    if not inputs_dict:
        # This should not be reached if sequence_names is provided and vocab_sizes are valid,
        # or if the dummy input fallback for no sequences was triggered.
        raise ValueError("inputs_dict is empty. Model requires at least one input.")
        
    model = tf.keras.Model(inputs=inputs_dict, outputs=outputs)
    
    return model

if __name__ == '__main__':
    # Example Usage (for testing the build function)
    MAXLEN = 64
    VOCAB_SIZES = { # 示例词汇表大小
        "user_core_action_code_seq": 1000,
        "user_core_action_day_seq": 200 
    }
    SEQUENCE_NAMES = ["user_core_action_code_seq", "user_core_action_day_seq"]
    STATIC_FEATURE_DIM = 50 # 示例静态特征维度
    EMBED_DIM = 128
    NUM_HEADS = 4
    FF_DIM = 128
    NUM_TRANSFORMER_BLOCKS = 2
    MLP_UNITS = [64]
    DROPOUT_RATE = 0.1
    MLP_DROPOUT = 0.1
    USE_ENHANCED_MODEL = True

    model = build_user_embedding_model(
        vocab_sizes=VOCAB_SIZES,
        sequence_names=SEQUENCE_NAMES,
        static_feature_dim=STATIC_FEATURE_DIM,
        embed_dim=EMBED_DIM,
        num_heads=NUM_HEADS,
        ff_dim=FF_DIM,
        num_transformer_blocks=NUM_TRANSFORMER_BLOCKS,
        maxlen=MAXLEN,
        mlp_units=MLP_UNITS,
        dropout_rate=DROPOUT_RATE,
        mlp_dropout=MLP_DROPOUT,
        use_enhanced_model=USE_ENHANCED_MODEL
    )

    model.summary()
    # print("-" * 20)
    # extractor.summary() # extractor 在build_user_embedding_model中不再返回

    # Test model prediction shapes
    batch_size = 4
    dummy_inputs = {}
    for seq_name in SEQUENCE_NAMES:
        dummy_inputs[f"{seq_name}_ids"] = tf.random.uniform(
            (batch_size, MAXLEN), 
            maxval=VOCAB_SIZES[seq_name], 
            dtype=tf.int32
        )
    if STATIC_FEATURE_DIM > 0:
        dummy_inputs["static_features"] = tf.random.uniform((batch_size, STATIC_FEATURE_DIM), dtype=tf.float32)

    outputs = model(dummy_inputs)
    print("\nModel output shapes:")
    print(f"  Classifier output: {outputs[0].shape}")
    print(f"  Embedding output: {outputs[1].shape}") # Should be (batch_size, embed_dim) 