import pandas as pd
import numpy as np
import json
from pathlib import Path
import logging
import glob
import pickle
import faiss # Needs installation: pip install faiss-cpu or faiss-gpu
import os
from datetime import datetime
import argparse
from scipy.spatial.distance import cosine
import matplotlib.pyplot as plt
import seaborn as sns

# --- Global Constants ---
USER_ID_COL = "user_id"
# This will be the target label we check for in retrieved items
# It should match what embedding_model_train.py -> prepare_dataset creates
DEFAULT_LABEL_COL = "target_purchase_next_30d" 
DEFAULT_CANDIDATE_DATE = "20240531" # Default date for candidate user data
DEFAULT_DATASET_BASE_PATH = Path("data/dataset_nio_new_car_v15")

# 索引类型常量
INDEX_TYPE_FLAT = "flat"     # 精确搜索，适用于小规模数据
INDEX_TYPE_IVF = "ivf"       # 倒排索引，适用于中等规模数据
INDEX_TYPE_HNSW = "hnsw"     # 层次可导航小世界图，适用于大规模数据
INDEX_TYPE_AUTO = "auto"     # 自动选择最佳索引类型

# --- Logging Setup ---
def setup_logging(base_output_dir: Path):
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = base_output_dir / f"retrieval_{timestamp}.log"
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(formatter)
    
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)
    logging.info(f"Retrieval logs will be saved to: {log_file}")
    return log_file

# --- Data Loading Functions ---
def load_user_embeddings(embeddings_file_path: Path):
    """加载用户ID和embedding向量"""
    if not embeddings_file_path.exists():
        logging.error(f"Embedding文件未找到: {embeddings_file_path}")
        return None, None
    try:
        with open(embeddings_file_path, 'rb') as f:
            data = pickle.load(f)
        # 预期格式: 包含'user_ids'和'embeddings'的字典
        if isinstance(data, dict) and 'user_ids' in data and 'embeddings' in data:
             user_ids = np.array(data['user_ids'])
             embeddings = np.array(data['embeddings'])
             logging.info(f"从{embeddings_file_path}加载了{len(user_ids)}个用户embedding")
             if embeddings.ndim == 2:
                 return user_ids, embeddings
             else:
                 logging.error(f"{embeddings_file_path}中的embedding数组维度不是2 (形状: {embeddings.shape}).")
                 return None, None

        # 支持旧格式(元组)
        elif isinstance(data, tuple) and len(data) == 2:
            logging.warning("从旧的元组格式加载embedding。建议重新保存为字典格式{'user_ids': ..., 'embeddings': ...}")
            user_ids, embeddings = data
            user_ids = np.array(user_ids)
            embeddings = np.array(embeddings)
            if embeddings.ndim == 2:
                 return user_ids, embeddings
            else:
                 logging.error(f"{embeddings_file_path}(元组格式)中的embedding数组维度不是2 (形状: {embeddings.shape}).")
                 return None, None
        else:
            logging.error(f"embedding文件{embeddings_file_path}格式不识别。期望包含'user_ids'和'embeddings'的字典或元组。")
            return None, None

    except Exception as e:
        logging.error(f"从{embeddings_file_path}加载embedding时出错: {e}")
        return None, None

def load_candidate_data(candidate_data_dir: Path, user_id_col: str, label_col: str):
    """从指定目录加载候选用户数据（用户ID和标签）"""
    data_path = candidate_data_dir
    if not data_path.exists():
        logging.error(f"候选数据目录未找到: {data_path}")
        return None

    files_to_load = glob.glob(str(data_path / "**/*.parquet"), recursive=True)
    if not files_to_load:
        logging.error(f"在{data_path}中未找到Parquet文件")
        return None
    
    logging.info(f"从{data_path}加载{len(files_to_load)}个候选数据文件...")
    
    df_list = []
    required_cols = [user_id_col, label_col] 
    
    raw_label_col_for_processing = "m_purchase_days_nio_new_car"

    for f_path in files_to_load:
        try:
            cols_to_read_from_parquet = {user_id_col}
            if raw_label_col_for_processing != label_col and label_col == 'target_purchase_next_30d': 
                 cols_to_read_from_parquet.add(raw_label_col_for_processing)
            else: 
                 cols_to_read_from_parquet.add(label_col)

            df_part = pd.read_parquet(f_path, columns=list(cols_to_read_from_parquet))
            
            # 处理原始标签
            if label_col == 'target_purchase_next_30d' and raw_label_col_for_processing in df_part.columns:
                def safe_parse_purchase_days(val):
                    if isinstance(val, list) and len(val) > 0 and val[0] == 1: return 1
                    if isinstance(val, str):
                        try: 
                            parsed_val = json.loads(val)
                            if isinstance(parsed_val, list) and len(parsed_val) > 0 and parsed_val[0] == 1: return 1
                        except: return 0
                    return 0
                df_part[label_col] = df_part[raw_label_col_for_processing].apply(safe_parse_purchase_days)
            
            if user_id_col not in df_part.columns or label_col not in df_part.columns:
                logging.warning(f"文件{f_path}缺少必要列({user_id_col}或{label_col})。实际列: {df_part.columns}。跳过。")
                continue
            df_list.append(df_part[required_cols])
        except Exception as e:
            logging.error(f"加载或处理候选文件{f_path}时出错: {e}")
            
    if not df_list:
        logging.error("未能加载任何有效的候选数据。")
        return None
        
    candidate_df = pd.concat(df_list, ignore_index=True).drop_duplicates(subset=[user_id_col])
    logging.info(f"已加载{len(candidate_df)}个唯一候选用户及其标签。")
    return candidate_df

# --- Faiss and Retrieval Functions ---
def build_optimal_index(embeddings, index_type="auto", data_size=None):
    """根据数据规模和需求构建最优索引"""
    if embeddings is None or embeddings.shape[0] == 0:
        logging.error("无法构建Faiss索引: 未提供embedding。")
        return None
        
    dimension = embeddings.shape[1]
    num_vectors = embeddings.shape[0]
    
    # 如果未指定数据大小，使用embedding数量
    if data_size is None:
        data_size = num_vectors
    
    # 根据数据规模自动选择最佳索引类型
    if index_type == "auto":
        if data_size < 100000:  # 小于10万条数据
            index_type = "flat"
        elif data_size < 1000000:  # 小于100万条数据
            index_type = "ivf"
        else:  # 大于100万条数据
            index_type = "hnsw"
        logging.info(f"根据数据规模({data_size}条)自动选择索引类型: {index_type}")
    
    try:
        if index_type == "flat":
            # 精确搜索 - 适用于小数据集
            index = faiss.IndexFlatL2(dimension)
            logging.info(f"构建精确搜索Flat索引，向量维度: {dimension}")
            
        elif index_type == "ivf":
            # IVF倒排索引 - 中等数据集
            nlist = min(int(np.sqrt(num_vectors)), 1024)
            quantizer = faiss.IndexFlatL2(dimension)
            index = faiss.IndexIVFFlat(quantizer, dimension, nlist)
            
            # IVF索引需要训练
            logging.info(f"训练IVF索引，聚类数: {nlist}...")
            index.train(embeddings.astype(np.float32))
            
            # 设置搜索参数
            index.nprobe = min(nlist // 4, 100)
            logging.info(f"IVF索引nprobe设置为: {index.nprobe}")
            
        elif index_type == "hnsw":
            # HNSW图索引 - 大数据集
            m = 32  # 较高的M值增加图的连接度，提高召回率
            ef_construction = 200
            ef_search = 128  # 较高的efSearch增加搜索范围
            
            index = faiss.IndexHNSWFlat(dimension, m)
            index.hnsw.efConstruction = ef_construction
            index.hnsw.efSearch = ef_search
            logging.info(f"构建HNSW索引，参数: m={m}, efConstruction={ef_construction}, efSearch={ef_search}")
            
        else:
            logging.error(f"未知索引类型: {index_type}，回退到flat")
            index = faiss.IndexFlatL2(dimension)
            
        # 添加向量到索引
        index.add(embeddings.astype(np.float32))
        logging.info(f"构建了{index_type}索引，包含{index.ntotal}个向量，维度为{dimension}。")
        
        return index
        
    except Exception as e:
        logging.error(f"构建{index_type}索引时出错: {e}")
        # 构建失败时回退到基本索引
        try:
            index = faiss.IndexFlatL2(dimension)
            index.add(embeddings.astype(np.float32))
            logging.warning(f"索引构建失败后回退到基本FlatL2索引: {e}")
            return index
        except Exception as e2:
            logging.error(f"索引构建严重失败: {e2}")
            return None

def search_similar_users(faiss_index, embeddings, user_ids, seed_user_ids, top_k=50, 
                          two_stage=False, first_stage_multiplier=3):
    """搜索相似用户，支持两阶段检索策略"""
    if faiss_index is None:
        logging.error("Faiss索引不可用，无法执行检索。")
        return None
    
    # 创建用户ID索引映射
    id_to_idx = {str(uid): i for i, uid in enumerate(user_ids)}
    
    # 获取种子用户的embedding
    seed_indices = []
    seed_ids_for_query = []
    for seed_id in seed_user_ids:
        str_seed_id = str(seed_id)
        if str_seed_id in id_to_idx:
            seed_indices.append(id_to_idx[str_seed_id])
            seed_ids_for_query.append(seed_id)
    
    if not seed_indices:
        logging.error("未找到有效的种子用户embedding，无法执行检索。")
        return {}
    
    seed_embeddings = embeddings[seed_indices]
    logging.info(f"为{len(seed_indices)}个种子用户检索相似用户...")
    
    retrieval_results = {}
    
    if two_stage:
        # 两阶段检索: 先获取更多候选，然后重排序
        logging.info(f"执行两阶段检索，第一阶段倍率: {first_stage_multiplier}")
        first_stage_k = min(top_k * first_stage_multiplier, faiss_index.ntotal)
        distances, indices = faiss_index.search(seed_embeddings.astype(np.float32), first_stage_k)
        
        for i, seed_id in enumerate(seed_ids_for_query):
            # 获取该种子用户的所有候选
            candidates = []
            for j in range(indices.shape[1]):
                retrieved_idx = indices[i, j]
                if retrieved_idx == -1:
                    continue
                # 排除自身
                retrieved_id = user_ids[retrieved_idx]
                if str(retrieved_id) == str(seed_id):
                    continue
                candidates.append((retrieved_id, float(distances[i, j])))
            
            # 重排序并保留top_k
            candidates.sort(key=lambda x: x[1])
            retrieval_results[str(seed_id)] = candidates[:top_k]
    else:
        # 标准检索
        # 为了处理自检索问题，获取比请求稍多的结果
        distances, indices = faiss_index.search(seed_embeddings.astype(np.float32), top_k + 1)
        
        for i, seed_id in enumerate(seed_ids_for_query):
            results_for_seed = []
            for j in range(indices.shape[1]):
                retrieved_idx = indices[i, j]
                if retrieved_idx == -1:
                    continue
                # 排除自身
                retrieved_id = user_ids[retrieved_idx]
                if str(retrieved_id) == str(seed_id):
                    continue
                results_for_seed.append((retrieved_id, float(distances[i, j])))
                if len(results_for_seed) >= top_k:
                    break
            retrieval_results[str(seed_id)] = results_for_seed
    
    return retrieval_results

def identify_seed_users(candidate_data_dir, user_id_col, label_col):
    """Identify seed users (users with label=1)"""
    candidate_df = load_candidate_data(candidate_data_dir, user_id_col, label_col)
    if candidate_df is None or candidate_df.empty:
        logging.warning("Failed to load candidate data for seed user identification.")
        return []
    
    # Filter users with label=1 as seeds
    if label_col in candidate_df.columns:
        seed_users = candidate_df[candidate_df[label_col] == 1][user_id_col].unique()
        logging.info(f"Identified {len(seed_users)} seed users based on label.")
        return seed_users
    else:
        logging.warning(f"Label column '{label_col}' not found in candidate data. Cannot identify seed users.")
        return []

# --- Evaluation Metrics ---
def calculate_retrieval_evaluation_metrics(
    retrieval_results, 
    candidate_df, 
    user_id_col, 
    label_col, 
    k_values,
    business_metrics=True
):
    """Calculate retrieval evaluation metrics"""
    metrics = {"precision_at_k": {}, "recall_at_k": {}, "business_metrics": {}}
    if not retrieval_results:
        logging.warning("No retrieval results to evaluate.")
        return metrics

    # Create mapping from user IDs to labels
    true_labels_map = pd.Series(candidate_df[label_col].values, index=candidate_df[user_id_col]).to_dict()
    
    # Calculate total relevant items in candidate set (global recall denominator)
    all_relevant_candidate_ids = set(candidate_df[candidate_df[label_col] == 1][user_id_col])
    total_relevant_in_candidates = len(all_relevant_candidate_ids)
    logging.info(f"Total relevant items in candidate set for global recall (label=1): {total_relevant_in_candidates}")

    # Calculate metrics for different K values
    for k_eval in k_values:
        # Initialize statistics for each K value
        sum_precision_at_k = 0.0
        num_valid_seed_users = 0
        distinct_relevant_retrieved_ids = set()

        # Process retrieval results for each seed user
        for seed_id, retrieved_items in retrieval_results.items():
            if not retrieved_items: continue
            num_valid_seed_users += 1

            # Consider only top-k results
            top_k_retrieved_ids = [item[0] for item in retrieved_items[:k_eval]]
            
            # Calculate hits (precision numerator)
            hits_at_k = 0
            for retrieved_user_id in top_k_retrieved_ids:
                if true_labels_map.get(retrieved_user_id, 0) == 1:
                    hits_at_k += 1
                    distinct_relevant_retrieved_ids.add(retrieved_user_id)
            
            # Accumulate precision
            precision_at_k = (hits_at_k / k_eval) if k_eval > 0 else 0
            sum_precision_at_k += precision_at_k
            
        # Calculate mean precision
        mean_precision_at_k = (sum_precision_at_k / num_valid_seed_users) if num_valid_seed_users > 0 else 0
        metrics["precision_at_k"][f"P@{k_eval}"] = mean_precision_at_k
        
        # Calculate global recall
        num_distinct_relevant_retrieved = len(distinct_relevant_retrieved_ids)
        global_recall_at_k = (num_distinct_relevant_retrieved / total_relevant_in_candidates) if total_relevant_in_candidates > 0 else 0
        metrics["recall_at_k"][f"R@{k_eval}"] = global_recall_at_k

        logging.info(f"Metrics@{k_eval}: Mean Precision={mean_precision_at_k:.4f} (based on {num_valid_seed_users} seeds), "
                     f"Global Recall={global_recall_at_k:.4f} (hits={num_distinct_relevant_retrieved}/total={total_relevant_in_candidates})")

    # Calculate business-related metrics
    if business_metrics and retrieval_results:
        # 1. Calculate average retrieval distance
        all_distances = []
        for seed_id, retrieved_items in retrieval_results.items():
            for _, distance in retrieved_items:
                all_distances.append(distance)
        
        if all_distances:
            metrics["business_metrics"]["avg_distance"] = sum(all_distances) / len(all_distances)
            metrics["business_metrics"]["min_distance"] = min(all_distances)
            metrics["business_metrics"]["max_distance"] = max(all_distances)
        
        # 2. Calculate seed user coverage - for how many seed users we can retrieve at least one relevant user
        covered_seeds = 0
        for seed_id, retrieved_items in retrieval_results.items():
            if not retrieved_items:
                continue
                
            for retrieved_id, _ in retrieved_items:
                if true_labels_map.get(retrieved_id, 0) == 1:
                    covered_seeds += 1
                    break
        
        if retrieval_results:
            metrics["business_metrics"]["seed_coverage"] = covered_seeds / len(retrieval_results)
    
    return metrics

# --- Visualization Functions ---
def generate_basic_visualizations(retrieval_results, evaluation_metrics, base_output_dir: Path):
    """Generate basic visualization charts"""
    viz_dir = base_output_dir / "visualizations"
    viz_dir.mkdir(parents=True, exist_ok=True)
    
    plt.style.use('seaborn-v0_8-whitegrid')
    
    if "recall_at_k" in evaluation_metrics:
        plt.figure(figsize=(10, 6))
        recall_at_k = evaluation_metrics["recall_at_k"]
        k_values = sorted([int(k.split('@')[1]) for k in recall_at_k.keys()])
        recall_values = [recall_at_k[f"R@{k}"] for k in k_values]
        
        plt.plot(k_values, recall_values, marker='o', linestyle='-', linewidth=2)
        plt.xlabel('K Value')
        plt.ylabel('Global Recall Rate')
        plt.title('Global Recall Rate at Different K values')
        plt.grid(True, alpha=0.3)
        
        for i, (k, value) in enumerate(zip(k_values, recall_values)):
            plt.annotate(f'{value:.3f}', (k, value), textcoords="offset points", xytext=(0,10), ha='center')
        
        plt.savefig(viz_dir / "recall_curve.png", dpi=300, bbox_inches='tight')
        plt.close()
        logging.info(f"Recall curve saved to: {viz_dir / 'recall_curve.png'}")
    
    distances = []
    for seed_id, items in retrieval_results.items():
        for item_id, distance in items:
            distances.append(distance)
    
    if distances:
        plt.figure(figsize=(10, 6))
        plt.hist(distances, bins=30, alpha=0.7)
        plt.xlabel('Distance')
        plt.ylabel('Frequency')
        plt.title('Distribution of Retrieval Distances')
        plt.grid(True, alpha=0.3)
        
        stats_text = f"Mean: {np.mean(distances):.3f}, Median: {np.median(distances):.3f}, StdDev: {np.std(distances):.3f}"
        plt.annotate(stats_text, xy=(0.5, 0.95), xycoords='axes fraction', 
                    ha='center', va='center', bbox=dict(boxstyle="round,pad=0.3", alpha=0.2))
        
        plt.savefig(viz_dir / "distance_distribution.png", dpi=300, bbox_inches='tight')
        plt.close()
        logging.info(f"Distance distribution plot saved to: {viz_dir / 'distance_distribution.png'}")

def display_key_metrics(metrics):
    """Display key metrics in console"""
    print("\n===== Retrieval Key Metrics Summary =====")
    
    if "recall_at_k" in metrics:
        print("\nRecall (Recall@K):")
        for k, value in metrics["recall_at_k"].items():
            print(f"  {k}: {value:.4f}")
    
    if "precision_at_k" in metrics:
        print("\nPrecision (Precision@K):")
        for k, value in metrics["precision_at_k"].items():
            print(f"  {k}: {value:.4f}")
    
    if "business_metrics" in metrics and metrics["business_metrics"]:
        print("\nBusiness Metrics:")
        for name, value in metrics["business_metrics"].items():
            print(f"  {name}: {value:.4f}")
    
    print("\n=============================\n")

# --- Main Orchestration ---
def main(args):
    # Use the output_dir directly as the base for all outputs 
    # (e.g., logs/my_test_run_XXX/)
    run_base_dir = Path(args.output_dir)
    
    # Ensure the base directory exists
    run_base_dir.mkdir(parents=True, exist_ok=True)
    
    log_file = setup_logging(run_base_dir)
    logging.info(f"Starting retrieval process, arguments: {args}")
    logging.info(f"All retrieval outputs will be saved directly under: {run_base_dir}")

    embeddings_file = Path(args.embeddings_file)
    user_ids, embeddings = load_user_embeddings(embeddings_file)
    if user_ids is None: 
        logging.error("Failed to load user embeddings. Exiting.")
        exit(1)
    
    candidate_df = load_candidate_data(
        Path(args.candidate_data_dir),
        args.user_id_col, 
        args.label_col,
    )
    
    if candidate_df is None or candidate_df.empty:
        logging.warning("Could not load candidate data. Proceeding with all users from embeddings file as candidates (no labels).")
        candidate_df = pd.DataFrame({args.user_id_col: user_ids, args.label_col: 0}) # Default label to 0
    
    seed_user_ids_list = None
    if args.seed_user_ids:
        try:
            seed_user_ids_list = [int(uid) for uid in args.seed_user_ids]
            logging.info(f"Using {len(seed_user_ids_list)} seed users specified via command line.")
        except ValueError:
            logging.error("Error parsing command-line seed user IDs. Will attempt to identify seeds based on labels.")
            seed_user_ids_list = None
    
    if not seed_user_ids_list: # If None or empty list from command line
        if args.label_col in candidate_df.columns:
            seed_user_ids_list = candidate_df[candidate_df[args.label_col] == 1][args.user_id_col].unique().tolist()
            logging.info(f"Identified {len(seed_user_ids_list)} seed users based on label column '{args.label_col}'.")
        else:
            logging.warning(f"Label column '{args.label_col}' not found in candidate data. Cannot identify seed users by label.")
            seed_user_ids_list = []
    
    if not seed_user_ids_list:
        logging.error("No seed users identified. Cannot proceed with retrieval.")
        exit(1)
    
    faiss_index = build_optimal_index(
        embeddings, 
        index_type=args.index_type,
        data_size=len(embeddings) # Using total embeddings count for auto-selection logic
    )
    
    if faiss_index is None:
        logging.error("Failed to build Faiss index. Cannot proceed.")
        exit(1)
    
    retrieval_results = search_similar_users(
        faiss_index,
        embeddings,
        user_ids,
        seed_user_ids_list, # Use the identified list
        top_k=args.top_k_retrieval,
        two_stage=args.two_stage_retrieval,
        first_stage_multiplier=args.first_stage_multiplier
    )
    
    if not retrieval_results:
        logging.error("Retrieval did not return any results.")
        # Optionally exit, or allow to proceed to save empty metrics if that's desired
        # exit(1) 
    
    retrieval_results_path = run_base_dir / "retrieval_results.json"
    with open(retrieval_results_path, 'w') as f:
        json.dump(retrieval_results, f, indent=4)
    logging.info(f"Retrieval results saved to: {retrieval_results_path}")
    
    # Initialize evaluation_metrics in case it's not calculated
    evaluation_metrics = {}

    if args.label_col in candidate_df.columns and retrieval_results: # Ensure results exist for eval
        eval_k_values_list = args.eval_k_values
        if not eval_k_values_list:
            eval_k_values_list = [10, 20, 50, 100]
            if args.top_k_retrieval not in eval_k_values_list:
                eval_k_values_list.append(args.top_k_retrieval)
            eval_k_values_list = sorted(list(set(eval_k_values_list))) # Deduplicate and sort
        
        evaluation_metrics = calculate_retrieval_evaluation_metrics(
            retrieval_results,
            candidate_df,
            args.user_id_col,
            args.label_col,
            eval_k_values_list,
            business_metrics=True
        )
        
        metrics_output_path = run_base_dir / "retrieval_evaluation_metrics.json"
        with open(metrics_output_path, 'w') as f:
            json.dump(evaluation_metrics, f, indent=4)
        logging.info(f"Retrieval evaluation metrics saved to: {metrics_output_path}")
        
        display_key_metrics(evaluation_metrics) # Display metrics if calculated
    else:
        if not retrieval_results:
            logging.warning("Skipping evaluation metrics calculation as no retrieval results were generated.")
        else:
            logging.warning(f"Label column '{args.label_col}' not found in candidate data. Skipping evaluation metrics calculation.")

    if args.auto_visualize and retrieval_results and evaluation_metrics: # Check if metrics were calculated for viz
        generate_basic_visualizations(retrieval_results, evaluation_metrics, run_base_dir)
    elif args.auto_visualize:
        logging.warning("Skipping visualization as retrieval results or evaluation metrics are missing.")

    logging.info(f"Retrieval script execution finished. Outputs are in: {run_base_dir}")
    logging.info(f"Main retrieval log file: {log_file}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="User Embedding Retrieval with Faiss")
    
    parser.add_argument("--embeddings_file", type=str, required=True, help="Path to the .pkl file containing user IDs and embeddings.")
    parser.add_argument("--candidate_data_dir", type=str, required=True, help="Directory of candidate user Parquet files.")
    parser.add_argument("--output_dir", type=str, required=True, help="Output directory for retrieval results (e.g., logs/my_test_run_XXX). All outputs will be saved directly in this directory.")
    parser.add_argument("--user_id_col", type=str, default=USER_ID_COL, help=f"User ID column name (default: {USER_ID_COL})")
    parser.add_argument("--label_col", type=str, default=DEFAULT_LABEL_COL, help=f"Label column name in candidate data (default: {DEFAULT_LABEL_COL})")

    parser.add_argument("--top_k_retrieval", type=int, default=50, help="Number of similar users to retrieve for each seed user (default: 50)")
    parser.add_argument("--seed_user_ids", nargs="*", default=None, help="List of seed user IDs (optional; if not provided, seeds are identified based on labels)")
    parser.add_argument("--index_type", type=str, default=INDEX_TYPE_AUTO, 
                       choices=[INDEX_TYPE_AUTO, INDEX_TYPE_FLAT, INDEX_TYPE_IVF, INDEX_TYPE_HNSW],
                       help="Faiss index type (default: auto)")
    
    parser.add_argument("--two_stage_retrieval", action="store_true", default=False, 
                       help="Enable two-stage retrieval (recall more candidates then re-rank by distance)")
    parser.add_argument("--first_stage_multiplier", type=int, default=3, 
                       help="Multiplier for the first stage of two-stage retrieval (e.g., 3 means recall 3*top_k candidates) (default: 3)")
    
    parser.add_argument("--eval_k_values", nargs="*", type=int, default=None, 
                       help="List of K values for evaluation metrics (default: [10, 20, 50, 100, top_k_retrieval])")
    
    parser.add_argument("--auto_visualize", action="store_true", default=True,
                       help="Automatically generate basic visualization plots (default: True)")
    
    args = parser.parse_args()
    main(args) 