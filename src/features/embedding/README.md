# NIO 用户 Embedding 模型

## 1. 项目目标

开发一个用户 Embedding 模型，该模型能够捕捉用户的复杂行为模式和静态画像特征。最终目标是生成高质量的用户向量（Embeddings），用于：

1.  **购车用户预测**：作为监督学习任务的一部分，直接预测用户在未来一段时间内购买蔚来汽车的可能性。
2.  **相似用户召回与推荐**：利用生成的用户 Embedding，通过计算用户间的相似度（如余弦相似度），找到与特定种子用户（例如，近期已购车用户、高意向用户）相似的潜在客户，重点提升 `Recall@TopN` 指标，赋能精准营销。

## 2. 模型架构 (`embedding_model.py`)

模型采用深度学习方法，结合用户行为序列和静态画像数据，生成统一的用户 Embedding。

### 2.1 输入特征

*   **序列特征**:
    *   核心动作序列 (`user_core_action_code_seq`)：用户与蔚来相关的核心行为记录。
    *   动作发生天数序列 (`user_core_action_day_seq`)：对应核心行为发生的时间信息。
    *   可扩展包含其他用户行为序列（如车辆交互序列 `user_car_core_action_code_seq` 等）。
*   **静态特征**:
    *   数值型 (`STATIC_FEATURES_NUM`)：用户近期行为统计（如30/60/90天内核心动作数）、NIO Value 等。
    *   类别型 (`STATIC_FEATURES_CAT`)：用户性别、年龄段、常驻城市、意向状态/等级、社区身份等画像标签。

### 2.2 特征处理与融合

1.  **序列特征编码**:
    *   每个序列的 Token 通过 `TokenAndPositionEmbedding` 层（包含Token Embedding和可学习的位置Embedding）转换为向量。
    *   使用多层 `TransformerBlock`（基于自注意力机制，采用Pre-Normalization架构提高稳定性）对序列向量进行深度编码，捕捉上下文依赖。
    *   对每个编码后的序列使用 `AttentionPooling`（增强模型）或带掩码的全局平均池化（基础模型）得到该序列的固定维度表示。
2.  **多序列特征融合 (若使用)**:
    *   不同序列的表示向量会拼接，并通过一个门控机制（`Dense`层 + `sigmoid`）学习各自的重要性权重，然后加权融合。
3.  **静态特征处理**:
    *   数值型特征进行缺失值填充和标准化。
    *   类别型特征进行缺失值填充和独热编码。
    *   处理后的静态特征通过一个简单的 `Dense` 网络进行变换。
4.  **序列与静态特征融合**:
    *   **增强模型**: 采用自适应门控机制，动态学习序列特征和静态特征的融合权重。
    *   **基础模型**: 简单拼接后通过 `Dense` 层投影。
    *   融合后的向量即为用户的原始 Embedding (`merged_features`)。

### 2.3 输出层

*   **用户 Embedding (`embedding_output`)**: `merged_features` 直接作为用户 Embedding 输出，用于下游的相似用户召回等任务。
*   **分类器 (`classifier_output`)**: `merged_features` 进一步通过多层感知机 (MLP)，最后接一个 `Dense(1, activation="sigmoid")` 层，用于预测用户购车的概率（监督学习任务）。

## 3. 训练流程 (`embedding_model_train.py`)

### 3.1 数据准备

*   从 Parquet 文件按日期加载原始用户数据。
*   **词汇表构建**: 为各序列特征构建词汇表，处理低频词和未知词。
*   **序列处理**: 将 Token 序列转换为定长的 ID 序列（填充/截断）。
*   **静态特征预处理**: 标准化数值特征，独热编码类别特征，并保存预处理器以供推理时使用。
*   **目标标签生成**: 根据原始的购车行为相关字段（如 `m_purchase_days_nio_new_car`）生成二分类的目标标签 `target_purchase_next_30d`。
*   使用 `tf.data.Dataset` 构建高效的数据输入管道。

### 3.2 模型训练

*   **损失函数**:
    *   主要使用 `SigmoidFocalCrossEntropy` (Focal Loss) 处理潜在的类别不平衡问题。
    *   `embedding_output` 的损失权重设为0，因为其通过分类任务间接学习。
*   **优化器**: 使用 `AdamW` (Adam with Weight Decay)。
*   **回调函数**:
    *   `ModelCheckpoint`: 根据验证集上的 `classifier_output_auc` 保存最佳模型。
    *   `EarlyStopping`: 防止过拟合，在验证集性能不再提升时提前终止训练。
*   **评估指标**: 主要监控 `AUC` (Area Under ROC Curve)。

### 3.3 输出与推理

*   训练完成后，保存：
    *   最佳性能的完整模型 (`model.h5`)。
    *   用于提取 Embedding 的子模型 (`embedding_extractor.h5`)。
    *   训练配置 (`training_config.json`)。
    *   词汇表 (每个序列特征一个 `vocab_*.json`)。
    *   静态特征预处理器 (`static_preprocessor.pkl`)。
*   支持 `predict` 模式进行批量预测，`extract_embedding` 模式提取用户 Embedding。
*   `--auto-extract` 参数可以在训练后自动为训练集和测试集提取并保存 Embedding。

## 4. 关键文件

*   `src/features/embedding/embedding_model.py`: 定义模型的核心架构。
*   `src/features/embedding/embedding_model_train.py`: 包含数据处理、模型训练、评估和推理的完整流程。
*   `src/features/embedding/README.md`: 本文档。

## 5. 运行示例

所有输出将保存在项目根目录下的 `logs/<output_dir_name>/` 子目录中。

```bash
# 运行一次快速测试（少量数据，少量轮次）
python src/features/embedding/embedding_model_train.py \
    --output-dir my_embedding_run_test \
    --mode train \
    --epochs 3 \
    --max-files 1 \
    --auto-extract

# 使用更多默认参数进行标准训练 (可按需调整epochs, learning_rate等)
# python src/features/embedding/embedding_model_train.py \
# --output-dir my_embedding_run_001 \
# --mode train \
# --epochs 20 \
# --learning-rate 5e-5 \
# --batch-size 128 \
# --include-static-features \
# --use-enhanced-model \
# --auto-extract
```

## 6. 潜在改进与思考点

*   **数据平衡策略**: 脚本中定义了`create_balanced_dataset`但未在主流程调用。若Focal Loss不足，可考虑启用。
*   **`user_core_action_day_seq`处理**: 可探索更丰富的编码方式，而非简单Token Embedding。
*   **超参数调优**: `embed_dim`, `num_heads`, `ff_dim`, Transformer层数等需要实验调优。
*   **嵌入评估**: 需专门脚本评估`Recall@TopN`等基于相似度召回的指标。
*   **静态特征推理配置**: 可考虑将原始静态特征列名（`STATIC_FEATURES_NUM`, `STATIC_FEATURES_CAT`）存入`training_config.json`，使推理时加载更直接。

## 7. 依赖库
* TensorFlow 2.x
* Faiss (faiss-cpu or faiss-gpu)
* NumPy
* Pandas
* Scikit-learn
* Matplotlib
* Seaborn 


根据目录列表，`./logs/my_test_run_025/` 目录包含以下文件：

1. **模型和预处理器文件**：
   - `model.h5`（141MB）：完整的用户嵌入模型，用于训练和预测
   - `embedding_extractor.h5`（46MB）：从完整模型中提取的用于生成嵌入的部分
   - `static_preprocessor.pkl`（21KB）：用于处理静态特征的预处理器

2. **嵌入文件**：
   - `user_embeddings.pkl`（10MB）：当前的用户嵌入
   - `user_embeddings_20240430.pkl`（6.9MB）：2024年4月30日数据生成的嵌入
   - `user_embeddings_20240531.pkl`（10MB）：2024年5月31日数据生成的嵌入

3. **词汇表文件**：
   - 各种 `vocab_*.json` 文件：保存不同序列特征的词汇表映射

4. **日志和配置**：
   - `training_config.json`：训练配置
   - `training_history.json`：训练历史记录
   - `train_20250521_134345.log`：训练日志
   - `extract_embedding_20250521_140447.log`：嵌入提取日志

5. **可视化目录**：
   - `tensorboard_logs/`：保存训练和验证指标的TensorBoard日志

现在修改后的代码会将以下检索相关输出直接保存在 `./logs/my_test_run_025/` 目录下：
- `retrieval_YYYYMMDD_HHMMSS.log`：检索日志
- `retrieval_results.json`：检索结果
- `retrieval_evaluation_metrics.json`：评估指标
- `visualizations/`：可视化图表


python src/features/embedding/embedding_model_train.py --output-dir my_test_run --mode train --epochs 3 --max-files 1 --auto-extract

python src/features/embedding/embedding_model_train.py --output-dir my_test_run --mode extract_embedding --test-date 20240531 --max-files 1

python src/features/embedding/embedding_model_retrieval.py --embeddings_file logs/my_test_run/user_embeddings.pkl --candidate_data_dir data/dataset_nio_new_car_v15/datetime=20240531/ --output_dir ... --top_k_retrieval 50 --auto_visualize

完整工作流程如下：
训练模型：python src/features/embedding/embedding_model_train.py --output-dir my_test_run_XXX --mode train --epochs N
提取嵌入：python src/features/embedding/embedding_model_train.py --output-dir my_test_run_XXX --mode extract_embedding --test-date YYYYMMDD
检索相似用户：python src/features/embedding/embedding_model_retrieval.py --embeddings_file logs/my_test_run_XXX/user_embeddings_YYYYMMDD.pkl --candidate_data_dir data/... --output_dir logs/my_test_run_XXX --auto_visualize