#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
NIO用户embedding模型结果可视化脚本

此脚本用于生成丰富的可视化图表，分析embedding模型的性能和用户相似性。
"""

import os
import json
import argparse
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import pickle
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA
import logging
from datetime import datetime
import glob

# Global constants from other scripts if needed (e.g. for loading candidate data for labels)
USER_ID_COL = "user_id"
DEFAULT_LABEL_COL = "target_purchase_next_30d" # For coloring embeddings
DEFAULT_CANDIDATE_DATE = "20240531"
DEFAULT_DATASET_BASE_PATH = Path("data/dataset_nio_new_car_v15")

# Settings
plt.style.use('seaborn-v0_8-whitegrid') # Using an available seaborn style
sns.set_palette("pastel")

# Setup logging
logger = logging.getLogger(__name__)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler() # Log to console
    ]
)

def ensure_output_dir(output_dir_path: Path):
    output_dir_path.mkdir(parents=True, exist_ok=True)
    logger.info(f"Output directory ensured: {output_dir_path}")

# --- Data Loading Functions --- 
def load_embeddings_from_file(embeddings_path: Path):
    """Loads user_ids and embeddings from a .pkl file (dict format)."""
    if not embeddings_path.exists():
        logger.warning(f"Embeddings file not found: {embeddings_path}")
        return None, None
    try:
        with open(embeddings_path, 'rb') as f:
            data = pickle.load(f)
        if isinstance(data, dict) and 'user_ids' in data and 'embeddings' in data:
            user_ids = np.array(data['user_ids'])
            embeddings = np.array(data['embeddings'])
            logger.info(f"Loaded {len(user_ids)} embeddings from {embeddings_path}")
            return user_ids, embeddings.astype(np.float32)
        else:
            logger.error(f"Invalid format in embeddings file {embeddings_path}. Expected dict with 'user_ids' and 'embeddings'.")
            return None, None
    except Exception as e:
        logger.error(f"Error loading embeddings from {embeddings_path}: {e}")
        return None, None

def load_json_metrics(metrics_path: Path):
    """Loads metrics from a JSON file."""
    if not metrics_path.exists():
        logger.warning(f"Metrics file not found: {metrics_path}")
        return None
    try:
        with open(metrics_path, 'r') as f:
            metrics = json.load(f)
        logger.info(f"Loaded metrics from {metrics_path}")
        return metrics
    except Exception as e:
        logger.error(f"Error loading metrics from {metrics_path}: {e}")
        return None

# (Optional) Simplified candidate data loader for labels if PCA/t-SNE coloring is desired
def load_candidate_labels_for_viz(
    candidate_data_dir: Path,
    user_id_col: str, 
    label_col: str, 
    max_files: int = None
):
    data_path = candidate_data_dir
    if not data_path.exists():
        logger.warning(f"Candidate data path for labels not found: {data_path}")
        return pd.DataFrame()

    files_to_load = glob.glob(str(data_path / "**/*.parquet"), recursive=True)
    if not files_to_load:
        logger.warning(f"No Parquet files for labels found in {data_path}")
        return pd.DataFrame()
    if max_files: files_to_load = files_to_load[:max_files]
    
    df_list = []
    raw_label_col_for_processing = "m_purchase_days_nio_new_car"
    for f_path in files_to_load:
        try:
            cols_to_read = {user_id_col}
            if raw_label_col_for_processing != label_col and label_col == DEFAULT_LABEL_COL:
                 cols_to_read.add(raw_label_col_for_processing)
            else:
                 cols_to_read.add(label_col)
            df_part = pd.read_parquet(f_path, columns=list(cols_to_read))
            if label_col == DEFAULT_LABEL_COL and raw_label_col_for_processing in df_part.columns:
                def safe_parse_purchase_days(val):
                    if isinstance(val, list) and len(val) > 0 and val[0] == 1: return 1
                    if isinstance(val, str): 
                        try: parsed_val = json.loads(val)
                        except: return 0
                        if isinstance(parsed_val, list) and len(parsed_val) > 0 and parsed_val[0] == 1: return 1
                    return 0
                df_part[label_col] = df_part[raw_label_col_for_processing].apply(safe_parse_purchase_days)
            if user_id_col in df_part.columns and label_col in df_part.columns:
                df_list.append(df_part[[user_id_col, label_col]])
        except Exception as e:
            logger.error(f"Error loading label data from {f_path}: {e}")
    if not df_list: return pd.DataFrame()
    return pd.concat(df_list, ignore_index=True).drop_duplicates(subset=[user_id_col])

# --- Visualization Functions ---
def visualize_classification_metrics(metrics_data, output_dir: Path):
    """Visualizes classification metrics (ROC AUC, PR AUC, Precision@K, Recall@K)."""
    if not metrics_data: 
        logger.warning("No classification metrics data to visualize.")
        return

    logger.info("Generating classification metrics plots...")
    
    labels = []
    roc_aucs = []
    pr_aucs = []
    prec_at_k = []
    rec_at_k = []
    k_value_str = ""

    # Assuming metrics_data is the direct dict from evaluation_metrics.json
    if "ROC_AUC" in metrics_data: labels.append("ROC AUC"); roc_aucs.append(metrics_data["ROC_AUC"])
    if "PR_AUC" in metrics_data: labels.append("PR AUC"); pr_aucs.append(metrics_data["PR_AUC"])
    
    # Find Precision@K and Recall@K (assumes one K value is primary, e.g. Precision@840)
    for key, value in metrics_data.items():
        if "Precision@" in key and value is not None: labels.append(key); prec_at_k.append(value); k_value_str=key.split("@")[1]
        if "Recall@" in key and value is not None: labels.append(key); rec_at_k.append(value)

    all_metrics_for_bar = []
    all_labels_for_bar = []
    if roc_aucs: all_metrics_for_bar.extend(roc_aucs); all_labels_for_bar.append("ROC AUC")
    if pr_aucs: all_metrics_for_bar.extend(pr_aucs); all_labels_for_bar.append("PR AUC")
    if prec_at_k: all_metrics_for_bar.extend(prec_at_k); all_labels_for_bar.append(f"Precision@{k_value_str}")
    if rec_at_k: all_metrics_for_bar.extend(rec_at_k); all_labels_for_bar.append(f"Recall@{k_value_str}")

    if not all_metrics_for_bar:
        logger.warning("No plottable classification metrics found.")
        return

    plt.figure(figsize=(10, 6))
    bars = plt.bar(all_labels_for_bar, all_metrics_for_bar, color=sns.color_palette("viridis", len(all_labels_for_bar)))
    plt.ylabel('Score')
    plt.title('Classification Performance Metrics')
    plt.ylim(0, 1.05)
    for bar in bars:
        yval = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2.0, yval + 0.01, f'{yval:.3f}', ha='center', va='bottom')
    
    plot_path = output_dir / "classification_metrics_summary.png"
    plt.savefig(plot_path, dpi=300, bbox_inches='tight')
    plt.close()
    logger.info(f"Saved classification metrics plot to {plot_path}")

def visualize_retrieval_metrics_plot(retrieval_metrics_data, output_dir: Path, compare_metrics_data=None):
    """绘制检索指标随k变化的曲线图"""
    # 1. 创建一个带有子图的图形
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    axes = axes.flatten()
    
    # 2. 定义指标名称和轴索引映射
    metrics = [
        ('precision', 0, 'Precision@K (精确率)'),
        ('recall', 1, 'Recall@K (召回率)'),
        ('user_coverage', 2, 'User Coverage@K (用户覆盖率)'),
        ('positive_rate', 3, 'Positive Rate@K (正样本率)')
    ]
    
    # 3. 为每个指标创建一条曲线
    k_values = sorted([int(k) for k in retrieval_metrics_data.keys()])
    
    for metric_name, ax_idx, title in metrics:
        # 提取当前指标的值
        metric_values = [retrieval_metrics_data[str(k)].get(metric_name, 0) for k in k_values]
        
        # 绘制当前模型的曲线
        axes[ax_idx].plot(k_values, metric_values, 'o-', linewidth=2, label='当前模型')
        
        # 如果提供了比较数据，也绘制比较模型的曲线
        if compare_metrics_data:
            compare_values = [compare_metrics_data[str(k)].get(metric_name, 0) for k in k_values]
            axes[ax_idx].plot(k_values, compare_values, 's--', linewidth=2, label='对比模型')
        
        # 配置子图
        axes[ax_idx].set_xlabel('K值 (检索数量)', fontsize=12)
        axes[ax_idx].set_ylabel(title, fontsize=12)
        axes[ax_idx].set_title(title, fontsize=14)
        axes[ax_idx].grid(True, linestyle='--', alpha=0.7)
        
        if metric_name in ['precision', 'recall', 'positive_rate']:
            axes[ax_idx].set_ylim(0, 1.05)
        
        # 仅在第一个子图添加图例
        if ax_idx == 0:
            axes[ax_idx].legend(fontsize=12)
    
    # 4. 配置整体图形布局
    plt.tight_layout()
    plt.suptitle('检索性能随K值变化', fontsize=16, y=1.02)
    
    # 5. 保存图形
    output_path = output_dir / 'retrieval_metrics_by_k.png'
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    logging.info(f"检索指标图表已保存到: {output_path}")

def visualize_business_metrics(business_metrics, output_dir: Path, compare_data=None):
    """可视化业务相关指标，如销售、转化率等"""
    if not business_metrics:
        logger.warning("No business metrics to visualize.")
        return
    
    logger.info("Generating business metrics visualization...")
    
    # 提取指标
    metrics = []
    values = []
    for metric, value in business_metrics.items():
        if isinstance(value, (int, float)):
            metrics.append(metric)
            values.append(value)
    
    # 如果有比较数据
    comp_values = None
    if compare_data:
        comp_values = []
        for metric in metrics:
            if metric in compare_data:
                comp_values.append(compare_data[metric])
            else:
                comp_values.append(None)
    
    # 绘制条形图
    plt.figure(figsize=(12, 6))
    x = np.arange(len(metrics))
    width = 0.35
    
    bars1 = plt.bar(x - width/2, values, width, label='Current Method')
    if comp_values:
        bars2 = plt.bar(x + width/2, comp_values, width, label='Comparison Method')
    
    plt.xlabel('Metrics')
    plt.ylabel('Value')
    plt.title('Business Performance Metrics')
    plt.xticks(x, metrics, rotation=45, ha='right')
    plt.legend()
    
    # 在条形上标注值
    for i, bar in enumerate(bars1):
        plt.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 0.01,
                f'{values[i]:.3f}', ha='center', va='bottom')
    
    if comp_values:
        for i, bar in enumerate(bars2):
            if comp_values[i] is not None:
                plt.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 0.01,
                        f'{comp_values[i]:.3f}', ha='center', va='bottom')
    
    plt.tight_layout()
    plot_path = output_dir / "business_metrics.png"
    plt.savefig(plot_path, dpi=300, bbox_inches='tight')
    plt.close()
    logger.info(f"Saved business metrics plot to {plot_path}")

def visualize_embedding_distribution_plot(
    embeddings: np.ndarray, 
    user_ids: np.ndarray, 
    candidate_labels_df: pd.DataFrame, 
    output_dir: Path, 
    user_id_col: str, 
    label_col: str, 
    n_samples=2000,
    metric='euclidean',  # 新增距离度量选项
    perplexity=30,       # 新增t-SNE参数
    cluster_analysis=True # 新增聚类分析选项
):
    """使用PCA和t-SNE可视化embedding空间分布，增强版"""
    if embeddings is None or embeddings.shape[0] == 0:
        logger.warning("No embeddings provided for visualization.")
        return

    # 如果数据太大，采样
    if embeddings.shape[0] > n_samples:
        logger.info(f"Sampling {n_samples} embeddings for visualization from {embeddings.shape[0]} total")
        sample_indices = np.random.choice(embeddings.shape[0], n_samples, replace=False)
        sampled_embeddings = embeddings[sample_indices]
        sampled_user_ids = user_ids[sample_indices]
    else:
        sampled_embeddings = embeddings
        sampled_user_ids = user_ids
    
    # 为可视化准备标签
    has_labels = False
    if candidate_labels_df is not None and not candidate_labels_df.empty:
        try:
            # 创建用户ID到标签的映射
            id_to_label = candidate_labels_df.set_index(user_id_col)[label_col].to_dict()
            sampled_labels = np.array([id_to_label.get(uid, 0) for uid in sampled_user_ids])
            has_labels = True
            logger.info(f"Prepared labels for visualization. Positive samples: {sum(sampled_labels)}/{len(sampled_labels)}")
        except Exception as e:
            logger.error(f"Error preparing labels for visualization: {e}")
            has_labels = False
    
    # 创建多页可视化
    plt.figure(figsize=(20, 10))
    
    # 1. PCA降维
    try:
        logger.info("Computing PCA projection...")
        pca = PCA(n_components=2)
        pca_result = pca.fit_transform(sampled_embeddings)
        
        plt.subplot(1, 2, 1)
        if has_labels:
            # 使用标签着色
            plt.scatter(
                pca_result[:, 0], pca_result[:, 1], 
                c=sampled_labels, 
                cmap='viridis', alpha=0.6, s=30
            )
            plt.colorbar(label=label_col)
        else:
            plt.scatter(pca_result[:, 0], pca_result[:, 1], alpha=0.6, s=30)
        
        plt.title(f'PCA Projection of User Embeddings\nExplained Variance: {pca.explained_variance_ratio_.sum():.2f}')
        plt.xlabel(f'PC1 ({pca.explained_variance_ratio_[0]:.2f})')
        plt.ylabel(f'PC2 ({pca.explained_variance_ratio_[1]:.2f})')
    except Exception as e:
        logger.error(f"Error computing PCA: {e}")
    
    # 2. t-SNE降维
    try:
        logger.info(f"Computing t-SNE projection (metric={metric}, perplexity={perplexity})...")
        tsne = TSNE(n_components=2, perplexity=perplexity, metric=metric, n_jobs=-1, random_state=42)
        tsne_result = tsne.fit_transform(sampled_embeddings)
        
        plt.subplot(1, 2, 2)
        if has_labels:
            scatter = plt.scatter(
                tsne_result[:, 0], tsne_result[:, 1], 
                c=sampled_labels, 
                cmap='viridis', alpha=0.6, s=30
            )
            plt.colorbar(scatter, label=label_col)
            
            # 标记正例和负例
            pos_mask = sampled_labels == 1
            neg_mask = sampled_labels == 0
            
            # 计算聚类中心
            if sum(pos_mask) > 0:
                pos_center = np.mean(tsne_result[pos_mask], axis=0)
                plt.scatter(pos_center[0], pos_center[1], marker='*', s=300, color='red', 
                            edgecolors='black', alpha=0.8, label='Positive Center')
            
            if sum(neg_mask) > 0:
                neg_center = np.mean(tsne_result[neg_mask], axis=0)
                plt.scatter(neg_center[0], neg_center[1], marker='*', s=300, color='blue', 
                            edgecolors='black', alpha=0.8, label='Negative Center')
            
            plt.legend()
        else:
            plt.scatter(tsne_result[:, 0], tsne_result[:, 1], alpha=0.6, s=30)
        
        plt.title(f't-SNE Projection of User Embeddings\nMetric: {metric}, Perplexity: {perplexity}')
        plt.xlabel('t-SNE Dimension 1')
        plt.ylabel('t-SNE Dimension 2')
    except Exception as e:
        logger.error(f"Error computing t-SNE: {e}")
    
    plt.tight_layout()
    plot_path = output_dir / f"embedding_distribution_{metric}_p{perplexity}.png"
    plt.savefig(plot_path, dpi=300, bbox_inches='tight')
    plt.close()
    logger.info(f"Saved embedding distribution plot to {plot_path}")
    
    # 3. 聚类分析（可选）
    if cluster_analysis and has_labels:
        try:
            from sklearn.cluster import KMeans
            from sklearn.metrics import silhouette_score, davies_bouldin_score
            
            # 测试不同的聚类数
            cluster_metrics = {"n_clusters": [], "silhouette": [], "davies_bouldin": [], "inertia": []}
            
            plt.figure(figsize=(18, 6))
            for i, n_clusters in enumerate([2, 3, 5, 8, 10]):
                kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
                cluster_labels = kmeans.fit_predict(sampled_embeddings)
                
                # 计算聚类指标
                if n_clusters > 1:  # 只有当聚类数>1时才能计算silhouette和davies_bouldin
                    silhouette = silhouette_score(sampled_embeddings, cluster_labels)
                    davies_bouldin = davies_bouldin_score(sampled_embeddings, cluster_labels)
                    cluster_metrics["n_clusters"].append(n_clusters)
                    cluster_metrics["silhouette"].append(silhouette)
                    cluster_metrics["davies_bouldin"].append(davies_bouldin)
                    cluster_metrics["inertia"].append(kmeans.inertia_)
                
                # 可视化聚类结果（使用t-SNE投影）
                plt.subplot(1, 5, i+1)
                scatter = plt.scatter(
                    tsne_result[:, 0], tsne_result[:, 1], 
                    c=cluster_labels, cmap='tab10', alpha=0.6, s=30
                )
                
                # 标记聚类中心
                centers = kmeans.cluster_centers_
                # 我们不能直接在t-SNE空间中显示聚类中心（因为不能反向映射），所以跳过这部分
                
                # 计算每个聚类的目标占比
                cluster_stats = {}
                for cluster_id in range(n_clusters):
                    cluster_mask = cluster_labels == cluster_id
                    if sum(cluster_mask) > 0:
                        pos_ratio = np.sum(sampled_labels[cluster_mask]) / sum(cluster_mask)
                        cluster_stats[cluster_id] = {
                            "size": sum(cluster_mask),
                            "pos_ratio": pos_ratio
                        }
                
                title = f'K-Means Clustering (k={n_clusters})'
                if n_clusters > 1:
                    title += f'\nSilhouette: {silhouette:.3f}'
                plt.title(title)
                plt.axis('off')
            
            plt.tight_layout()
            plot_path = output_dir / "embedding_clustering_analysis.png"
            plt.savefig(plot_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            # 绘制聚类指标曲线
            plt.figure(figsize=(18, 6))
            plt.subplot(1, 3, 1)
            plt.plot(cluster_metrics["n_clusters"], cluster_metrics["silhouette"], marker='o')
            plt.title('Silhouette Score vs. Number of Clusters')
            plt.xlabel('Number of Clusters')
            plt.ylabel('Silhouette Score')
            plt.grid(True)
            
            plt.subplot(1, 3, 2)
            plt.plot(cluster_metrics["n_clusters"], cluster_metrics["davies_bouldin"], marker='o')
            plt.title('Davies-Bouldin Score vs. Number of Clusters')
            plt.xlabel('Number of Clusters')
            plt.ylabel('Davies-Bouldin Score')
            plt.grid(True)
            
            plt.subplot(1, 3, 3)
            plt.plot(cluster_metrics["n_clusters"], cluster_metrics["inertia"], marker='o')
            plt.title('Inertia vs. Number of Clusters')
            plt.xlabel('Number of Clusters')
            plt.ylabel('Inertia')
            plt.grid(True)
            
            plt.tight_layout()
            plot_path = output_dir / "clustering_metrics.png"
            plt.savefig(plot_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            logger.info(f"Saved clustering analysis plots")
            
        except Exception as e:
            logger.error(f"Error in cluster analysis: {e}")

def visualize_retrieved_item_distances(retrieval_results_data, output_dir: Path, compare_results=None):
    """可视化检索得到的item距离分布"""
    if not retrieval_results_data:
        logger.warning("No retrieval results to visualize distances.")
        return

    logger.info("Generating retrieved item distance distribution plots...")
    
    # 收集所有距离
    distances = []
    for seed_id, retrieved_items in retrieval_results_data.items():
        for item_id, distance in retrieved_items:
            distances.append(distance)
    
    comp_distances = []
    if compare_results:
        for seed_id, retrieved_items in compare_results.items():
            for item_id, distance in retrieved_items:
                comp_distances.append(distance)
    
    plt.figure(figsize=(12, 6))
    if comp_distances:
        plt.hist([distances, comp_distances], bins=30, alpha=0.7, 
                label=['Current Method', 'Comparison Method'])
    else:
        plt.hist(distances, bins=30, alpha=0.7)
    
    plt.xlabel('Distance')
    plt.ylabel('Frequency')
    plt.title('Distribution of Retrieved Item Distances')
    
    # 添加统计信息
    if distances:
        stats_text = f"Mean: {np.mean(distances):.3f}, Median: {np.median(distances):.3f}, StdDev: {np.std(distances):.3f}"
        if comp_distances:
            comp_stats = f"Comp Mean: {np.mean(comp_distances):.3f}, Comp Median: {np.median(comp_distances):.3f}"
            stats_text += f"\n{comp_stats}"
        plt.annotate(stats_text, xy=(0.5, 0.95), xycoords='axes fraction', 
                    ha='center', va='center', bbox=dict(boxstyle="round,pad=0.3", alpha=0.2))
    
    if comp_distances:
        plt.legend()
    
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plot_path = output_dir / "retrieved_item_distances.png"
    plt.savefig(plot_path, dpi=300, bbox_inches='tight')
    plt.close()
    logger.info(f"Saved retrieved item distances plot to {plot_path}")

def cluster_analysis(embeddings, user_ids, labels=None, output_dir=None, n_clusters=5, method='kmeans'):
    """对用户embedding进行聚类分析"""
    # ... 函数实现 ...

# --- Main Orchestration ---
def main(args):
    """主函数，协调各种可视化任务"""
    # 确保输出目录存在
    if args.output_dir:
        output_dir = Path(args.output_dir)
    else:
        output_dir = Path(args.model_output_dir) / "visualizations"
    
    ensure_output_dir(output_dir)
    logger.info(f"可视化结果将保存到: {output_dir}")
    
    # 1. 加载embedding
    user_ids, embeddings = None, None
    if args.load_embeddings:
        # 确定正确的embedding文件路径
        if args.embeddings_file:
            embeddings_file = Path(args.embeddings_file)
        else:
            # 尝试查找测试日期或标准名称的embedding文件
            if args.test_date:
                embeddings_file = Path(args.model_output_dir) / f"user_embeddings_{args.test_date}.pkl"
            else:
                embeddings_file = Path(args.model_output_dir) / "user_embeddings.pkl"
        
        # 加载embedding
        if embeddings_file.exists():
            user_ids, embeddings = load_embeddings_from_file(embeddings_file)
        else:
            logger.warning(f"未找到embedding文件: {embeddings_file}")
    
    # 2. 加载评估指标
    evaluation_metrics = None
    retrieval_metrics = None
    
    # 加载分类评估指标
    if args.model_output_dir:
        metrics_file = Path(args.model_output_dir) / "evaluation_metrics.json"
        if metrics_file.exists():
            evaluation_metrics = load_json_metrics(metrics_file)
            if evaluation_metrics:
                visualize_classification_metrics(evaluation_metrics, output_dir)
                logger.info("已生成分类评估指标可视化")
    
    # 可视化检索结果
    if args.load_retrieval_results:
        # 加载检索结果
        if args.retrieval_results_file:
            retrieval_results_file = Path(args.retrieval_results_file)
        else:
            retrieval_results_file = Path(args.model_output_dir) / "retrieval_results.json"
        
        # 加载比较结果(如果有)
        compare_results_file = None
        if args.compare_results_file:
            compare_results_file = Path(args.compare_results_file)
        
        retrieval_metrics = None
        compare_metrics = None
        
        if retrieval_results_file.exists():
            with open(retrieval_results_file, 'r') as f:
                retrieval_data = json.load(f)
                if "metrics" in retrieval_data:
                    retrieval_metrics = retrieval_data["metrics"]
            
            if compare_results_file and compare_results_file.exists():
                with open(compare_results_file, 'r') as f:
                    compare_data = json.load(f)
                    if "metrics" in compare_data:
                        compare_metrics = compare_data["metrics"]
            
            # 生成检索指标可视化
            if retrieval_metrics:
                visualize_retrieval_metrics_plot(retrieval_metrics, output_dir, compare_metrics)
                logging.info("已生成检索评估指标可视化")
    
    # 4. 加载用户标签数据（用于embedding可视化的着色）
    candidate_labels_df = pd.DataFrame()
    if embeddings is not None and args.visualize_with_labels and args.candidate_data_dir_for_viz:
        candidate_dir = Path(args.candidate_data_dir_for_viz)
        if candidate_dir.exists():
            candidate_labels_df = load_candidate_labels_for_viz(
                candidate_dir,
                USER_ID_COL,
                DEFAULT_LABEL_COL,
                args.max_candidate_files_viz
            )
            logger.info(f"加载了{len(candidate_labels_df)}条用户标签数据用于可视化")
    
    # 5. 执行embedding可视化
    if embeddings is not None and user_ids is not None:
        # 解析距离度量选项
        metrics_list = args.viz_metrics.split(',')
        # 解析perplexity选项
        perplexity_list = [int(p) for p in args.perplexity.split(',')] if args.perplexity else [30]
        
        for metric in metrics_list:
            for perplexity in perplexity_list:
                visualize_embedding_distribution_plot(
                    embeddings,
                    user_ids,
                    candidate_labels_df,
                    output_dir,
                    USER_ID_COL,
                    DEFAULT_LABEL_COL,
                    n_samples=args.max_samples_viz,
                    metric=metric.strip(),
                    perplexity=perplexity,
                    cluster_analysis=args.cluster_analysis
                )
                logger.info(f"已生成embedding分布可视化 (metric={metric}, perplexity={perplexity})")
    
    logger.info(f"所有可视化任务已完成，结果保存在: {output_dir}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="NIO用户Embedding模型可视化工具")
    
    # 基本参数
    parser.add_argument("--model_output_dir", type=str, required=True, help="模型输出目录(包含embedding、指标等)")
    parser.add_argument("--output_dir", type=str, default=None, help="可视化结果保存目录，默认为model_output_dir/visualizations")
    
    # Embedding加载参数
    parser.add_argument("--load_embeddings", action="store_true", default=True, help="是否加载并可视化embedding，默认: True")
    parser.add_argument("--embeddings_file", type=str, default=None, help="指定embedding文件路径(可选)")
    parser.add_argument("--test_date", type=str, default=None, help="测试日期，用于查找embedding文件(可选)")
    
    # 检索结果和指标参数
    parser.add_argument("--retrieval_metrics_file", type=str, default=None, help="检索评估指标JSON文件路径(可选)")
    parser.add_argument("--retrieval_results_file", type=str, default=None, help="检索结果JSON文件路径(可选)")
    parser.add_argument("--compare_retrieval_metrics_file", type=str, default=None, help="用于比较的检索评估指标JSON文件路径(可选)")
    parser.add_argument("--compare_retrieval_results_file", type=str, default=None, help="用于比较的检索结果JSON文件路径(可选)")
    
    # 标签数据参数(用于embedding可视化着色)
    parser.add_argument("--visualize_with_labels", action="store_true", default=False, help="是否使用标签为embedding着色")
    parser.add_argument("--candidate_data_dir_for_viz", type=str, default=None, help="用于可视化的候选用户数据目录")
    parser.add_argument("--max_candidate_files_viz", type=int, default=10, help="加载用于可视化标签的最大候选文件数")
    
    # 可视化参数
    parser.add_argument("--max_samples_viz", type=int, default=2000, help="embedding可视化采样的最大点数")
    parser.add_argument("--viz_metrics", type=str, default="euclidean", help="t-SNE距离度量(逗号分隔: euclidean,cosine)")
    parser.add_argument("--perplexity", type=str, default="30", help="t-SNE困惑度参数(逗号分隔，如: 30,50)")
    parser.add_argument("--cluster_analysis", action="store_true", default=False, help="是否执行embedding聚类分析")
    
    args = parser.parse_args()
    main(args) 