"""
自适应模型工厂 - 根据实际数据特征自动构建最优模型

设计理念：
1. 数据驱动：根据实际特征数据确定模型架构
2. 自动适配：无需手动配置复杂的特征映射
3. 类型推断：自动推断特征类型和处理方式
4. 一致性保证：确保特征处理与模型期望完全匹配
"""

import numpy as np
import pandas as pd
import tensorflow as tf
from typing import Dict, List, Tuple, Any, Optional
import logging
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class FeatureType(Enum):
    """特征类型枚举"""
    CATEGORICAL = "categorical"    # 类别特征
    NUMERICAL = "numerical"       # 数值特征  
    SEQUENCE = "sequence"         # 序列特征
    TEXT = "text"                # 文本特征
    EMBEDDING = "embedding"       # 嵌入特征


@dataclass
class FeatureSpec:
    """特征规格说明"""
    name: str
    feature_type: FeatureType
    vocab_size: Optional[int] = None
    embedding_dim: Optional[int] = None
    max_length: Optional[int] = None
    is_required: bool = True


@dataclass
class ModelArchitectureSpec:
    """模型架构规格"""
    general_features: List[FeatureSpec]
    sequence_features: List[FeatureSpec] 
    scene_features: List[FeatureSpec]
    output_dimension: int
    use_cross_layer: bool = True
    use_time_attention: bool = True


class DataDrivenFeatureAnalyzer:
    """数据驱动的特征分析器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def analyze_dataframe(self, df: pd.DataFrame, 
                         target_column: str = None) -> Dict[str, FeatureSpec]:
        """
        分析DataFrame，自动推断特征类型和规格
        
        Args:
            df: 输入数据框
            target_column: 目标列名（排除在特征分析外）
            
        Returns:
            特征名到特征规格的映射
        """
        feature_specs = {}
        
        for column in df.columns:
            if column == target_column:
                continue
                
            feature_spec = self._analyze_column(df[column], column)
            feature_specs[column] = feature_spec
            
        self.logger.info(f"分析完成，发现 {len(feature_specs)} 个特征")
        self._log_feature_summary(feature_specs)
        
        return feature_specs
    
    def _analyze_column(self, series: pd.Series, column_name: str) -> FeatureSpec:
        """分析单个列的特征类型"""
        
        # 检查是否为序列特征
        if self._is_sequence_feature(series, column_name):
            max_len = self._get_sequence_max_length(series)
            return FeatureSpec(
                name=column_name,
                feature_type=FeatureType.SEQUENCE,
                max_length=max_len
            )
        
        # 检查数据类型
        if pd.api.types.is_numeric_dtype(series):
            return FeatureSpec(
                name=column_name,
                feature_type=FeatureType.NUMERICAL
            )
        
        # 字符串类型，检查是否为类别特征
        if pd.api.types.is_string_dtype(series) or pd.api.types.is_object_dtype(series):
            # 过滤掉numpy数组和其他复杂对象，只处理简单字符串
            simple_series = series.dropna()
            simple_values = []
            for val in simple_series:
                if isinstance(val, (str, int, float)) and not isinstance(val, np.ndarray):
                    simple_values.append(str(val))
            
            if len(simple_values) == 0:
                # 如果没有简单值，跳过这个特征
                return FeatureSpec(
                    name=column_name,
                    feature_type=FeatureType.CATEGORICAL,
                    vocab_size=2,
                    embedding_dim=4
                )
            
            unique_values = len(set(simple_values))
            total_values = len(simple_values)
            
            # 如果唯一值比例小于30%，认为是类别特征
            if unique_values / total_values < 0.3:
                vocab_size = min(unique_values + 1, 10000)  # 限制词汇表大小
                embedding_dim = min(max(4, int(vocab_size ** 0.25)), 50)
                
                return FeatureSpec(
                    name=column_name,
                    feature_type=FeatureType.CATEGORICAL,
                    vocab_size=vocab_size,
                    embedding_dim=embedding_dim
                )
            else:
                # 高基数类别或文本
                return FeatureSpec(
                    name=column_name,
                    feature_type=FeatureType.TEXT,
                    vocab_size=min(unique_values + 1, 50000),
                    embedding_dim=64
                )
        
        # 默认作为类别特征处理
        return FeatureSpec(
            name=column_name,
            feature_type=FeatureType.CATEGORICAL,
            vocab_size=100,
            embedding_dim=8
        )
    
    def _is_sequence_feature(self, series: pd.Series, column_name: str) -> bool:
        """判断是否为序列特征"""
        # 检查列名模式
        sequence_patterns = ['_seq', '_sequence', 'action_code', 'action_day', '_model_seq']
        if any(pattern in column_name.lower() for pattern in sequence_patterns):
            return True
        
        # 检查数据内容（如果包含分隔符，可能是序列）
        if pd.api.types.is_string_dtype(series):
            sample_values = series.dropna().head(100)
            if len(sample_values) > 0:
                # 检查是否包含常见的序列分隔符
                delimiters = [',', ';', '|', ' ']
                for value in sample_values:
                    if isinstance(value, str):
                        for delim in delimiters:
                            if delim in value and len(value.split(delim)) > 1:
                                return True
        
        return False
    
    def _get_sequence_max_length(self, series: pd.Series) -> int:
        """获取序列的最大长度"""
        max_length = 0
        sample_values = series.dropna().head(1000)
        
        for value in sample_values:
            if isinstance(value, str):
                # 尝试不同的分隔符
                for delim in [',', ';', '|', ' ']:
                    if delim in value:
                        length = len(value.split(delim))
                        max_length = max(max_length, length)
                        break
        
        # 设置合理的上限
        return min(max_length, 100)
    
    def _log_feature_summary(self, feature_specs: Dict[str, FeatureSpec]):
        """记录特征分析摘要"""
        type_counts = {}
        for spec in feature_specs.values():
            type_name = spec.feature_type.value
            type_counts[type_name] = type_counts.get(type_name, 0) + 1
        
        self.logger.info("特征类型分布：")
        for feature_type, count in type_counts.items():
            self.logger.info(f"  {feature_type}: {count} 个特征")


class AdaptiveModelArchitectBuilder:
    """自适应模型架构构建器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def build_architecture_spec(self, 
                               feature_specs: Dict[str, FeatureSpec],
                               output_dimension: int = 6) -> ModelArchitectureSpec:
        """
        根据特征规格构建模型架构规格
        
        Args:
            feature_specs: 特征规格字典
            output_dimension: 输出维度
            
        Returns:
            模型架构规格
        """
        
        # 智能分组特征 - 优先保证关键决策特征分到一般特征
        general_features = []
        sequence_features = []
        scene_features = []
        
        # 关键决策特征列表，强制分配到一般特征
        key_decision_features = {
            'fellow_follow_decision_maker', 'fellow_follow_intention_nio_confirm', 
            'fellow_follow_intention_test_drive'
        }
        
        for spec in feature_specs.values():
            if spec.feature_type == FeatureType.SEQUENCE:
                sequence_features.append(spec)
            elif spec.name in key_decision_features:
                # 强制将关键决策特征分配到一般特征
                general_features.append(spec)
                self.logger.info(f"  ✅ 关键决策特征 {spec.name} 已强制分配到一般特征")
            elif self._is_scene_feature(spec.name):
                scene_features.append(spec)
            else:
                general_features.append(spec)
        
        # 决定是否使用高级功能
        use_time_attention = len(sequence_features) > 0
        use_cross_layer = len(general_features) > 10  # 特征多时使用交叉层
        
        self.logger.info(f"架构决策：")
        self.logger.info(f"  一般特征: {len(general_features)} 个")
        self.logger.info(f"  序列特征: {len(sequence_features)} 个")
        self.logger.info(f"  场景特征: {len(scene_features)} 个")
        self.logger.info(f"  使用时间注意力: {use_time_attention}")
        self.logger.info(f"  使用交叉层: {use_cross_layer}")
        
        return ModelArchitectureSpec(
            general_features=general_features,
            sequence_features=sequence_features,
            scene_features=scene_features,
            output_dimension=output_dimension,
            use_cross_layer=use_cross_layer,
            use_time_attention=use_time_attention
        )
    
    def _is_scene_feature(self, feature_name: str) -> bool:
        """判断是否为场景特征 - 修复错误的pattern匹配"""
        # 修复：不再使用'fellow_follow_'的pattern匹配，因为它会将
        # fellow_follow_decision_maker等关键决策特征错误地分类为场景特征
        conservative_scene_patterns = [
            'intention_stage', 'intention_status', 'intention_create_time',
            'user_core_nio_user_identity', 'user_core_user_age_group', 
            'user_core_user_gender', 'product_type'
        ]
        return any(pattern in feature_name.lower() for pattern in conservative_scene_patterns)


class AdaptiveModelFactory:
    """自适应模型工厂"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.feature_analyzer = DataDrivenFeatureAnalyzer()
        self.arch_builder = AdaptiveModelArchitectBuilder()
    
    def create_model_from_data(self, 
                              train_data: pd.DataFrame,
                              target_column: str,
                              model_config: Dict[str, Any] = None) -> Tuple[tf.keras.Model, Dict]:
        """
        从数据自动创建最优模型
        
        Args:
            train_data: 训练数据
            target_column: 目标列名
            model_config: 额外的模型配置
            
        Returns:
            (模型, 特征处理信息)
        """
        
        self.logger.info("🧠 开始数据驱动的模型构建...")
        
        # 1. 分析特征
        feature_specs = self.feature_analyzer.analyze_dataframe(train_data, target_column)
        
        # 2. 构建架构规格
        output_dim = model_config.get('output_dimension', 6) if model_config else 6
        arch_spec = self.arch_builder.build_architecture_spec(feature_specs, output_dim)
        
        # 3. 创建TensorFlow模型
        model, preprocessing_info = self._build_tensorflow_model(arch_spec, feature_specs)
        
        self.logger.info("✅ 自适应模型构建完成")
        
        return model, preprocessing_info
    
    def _build_tensorflow_model(self, 
                               arch_spec: ModelArchitectureSpec,
                               feature_specs: Dict[str, FeatureSpec]) -> Tuple[tf.keras.Model, Dict]:
        """构建TensorFlow模型"""
        
        inputs = {}
        embeddings = []
        preprocessing_info = {'feature_specs': feature_specs}
        
        # 构建输入层和嵌入层
        for spec in arch_spec.general_features:
            input_layer, embedding = self._create_feature_layers(spec)
            inputs[spec.name] = input_layer
            if embedding is not None:
                embeddings.append(embedding)
        
        for spec in arch_spec.sequence_features:
            input_layer, embedding = self._create_sequence_layers(spec)
            inputs[spec.name] = input_layer
            if embedding is not None:
                embeddings.append(embedding)
        
        for spec in arch_spec.scene_features:
            input_layer, embedding = self._create_feature_layers(spec)
            inputs[spec.name] = input_layer
            if embedding is not None:
                embeddings.append(embedding)
        
        # 合并所有嵌入
        if embeddings:
            merged = tf.keras.layers.Concatenate()(embeddings)
        else:
            # 如果没有特征，创建一个虚拟输入
            dummy_input = tf.keras.layers.Input(shape=(1,), name='dummy')
            inputs['dummy'] = dummy_input
            merged = dummy_input
        
        # 使用完整的EPMMOENet架构而非简化版
        try:
            from src.models.networks.EPMMOENet_enhanced import EnhancedEPMMOENet
            self.logger.info("使用完整的EnhancedEPMMOENet架构")
            
            # 构建符合EPMMOENet规范的model_config，映射正确的特征类型
            def map_feature_type(spec):
                """映射特征类型到EPMMOENet期望的格式"""
                if spec.feature_type == FeatureType.NUMERICAL:
                    return "Bucket"  # 数值特征使用分桶
                elif spec.feature_type == FeatureType.CATEGORICAL:
                    return "StringLookup"  # 类别特征使用字符串查找
                elif spec.feature_type == FeatureType.TEXT:
                    return "StringLookup"  # 文本特征也使用字符串查找
                elif spec.feature_type == FeatureType.SEQUENCE:
                    return "StringLookup"  # 序列特征使用字符串查找
                elif spec.feature_type == FeatureType.EMBEDDING:
                    return "Dense"  # 嵌入特征使用密集连接
                else:
                    return "StringLookup"  # 默认使用字符串查找
            
            # 构建特征配置字典，确保格式正确
            raw_features = {}
            for spec in list(arch_spec.general_features) + list(arch_spec.sequence_features) + list(arch_spec.scene_features):
                feature_type = map_feature_type(spec)
                feature_config = {
                    "dtype": feature_type,
                    "embedding_dimension": getattr(spec, 'embedding_dim', 8)
                }
                
                if feature_type == "StringLookup":
                    # 为StringLookup类型创建默认词汇表
                    vocab_size = getattr(spec, 'vocab_size', 100)
                    feature_config["vocabulary"] = [f"item_{i}" for i in range(min(vocab_size, 50))]
                elif feature_type == "Bucket":
                    # 为Bucket类型创建边界
                    feature_config["bin_boundarie"] = [0.5, 1.5, 2.5, 5.5, 10.5]
                    
                raw_features[spec.name] = feature_config
            
            model_config = {
                "output_dimension": arch_spec.output_dimension,
                "output_activation": "sigmoid",
                "mask_label": "mask_label",
                "RawFeature": raw_features,
                "InputGeneral": {"features": [spec.name for spec in arch_spec.general_features]},
                "InputScene": {"features": [spec.name for spec in arch_spec.scene_features]},
                "InputSeqSet": {
                    "Set": ["UserSeq"] if arch_spec.sequence_features else [],
                    "SetInfo": {"UserSeq": {
                        "features": [spec.name for spec in arch_spec.sequence_features],
                        "gru_dimension": 32
                    }} if arch_spec.sequence_features else {}
                }
            }
            
            # 构建完整的EnhancedEPMMOENet模型
            epmmoenet = EnhancedEPMMOENet(
                model_config=model_config,
                use_cross_layer=arch_spec.use_cross_layer,
                use_time_attention=arch_spec.use_time_attention,
                use_multitask=True,  # 启用多任务学习以获得更好性能
                default_embedding_dimension=8,
                default_gru_dimension=32
            )
            
            # 调用模型构建方法
            outputs = epmmoenet(inputs)
            model = tf.keras.Model(inputs=inputs, outputs=outputs)
            
        except Exception as e:
            self.logger.warning(f"EPMMOENet_Enhanced不可用: {e}，使用简化架构")
            # 回退到简化架构
            x = merged
            
            # 交叉层
            if arch_spec.use_cross_layer and len(embeddings) > 1:
                from src.models.layers.layers import CrossLayer
                x = CrossLayer()(x)
            
            # 全连接层
            x = tf.keras.layers.Dense(128, activation='relu')(x)
            x = tf.keras.layers.BatchNormalization()(x)
            x = tf.keras.layers.Dropout(0.3)(x)
            
            x = tf.keras.layers.Dense(64, activation='relu')(x)
            x = tf.keras.layers.BatchNormalization()(x)
            x = tf.keras.layers.Dropout(0.2)(x)
            
            # 输出层
            outputs = tf.keras.layers.Dense(
                arch_spec.output_dimension, 
                activation='sigmoid',
                name='prediction'
            )(x)
            
            model = tf.keras.Model(inputs=inputs, outputs=outputs)
        
        self.logger.info(f"模型构建完成，参数数量: {model.count_params():,}")
        
        return model, preprocessing_info
    
    def _create_feature_layers(self, spec: FeatureSpec) -> Tuple[tf.keras.layers.Input, tf.keras.layers.Layer]:
        """创建普通特征的输入和嵌入层"""
        
        if spec.feature_type == FeatureType.NUMERICAL:
            input_layer = tf.keras.layers.Input(shape=(), name=spec.name, dtype=tf.float32)
            # 数值特征归一化，使用Lambda层包装
            expanded = tf.keras.layers.Lambda(lambda x: tf.expand_dims(x, -1))(input_layer)
            normalized = tf.keras.layers.Normalization()(expanded)
            return input_layer, normalized
        
        elif spec.feature_type == FeatureType.CATEGORICAL:
            input_layer = tf.keras.layers.Input(shape=(), name=spec.name, dtype=tf.string)
            # 字符串查找 + 嵌入 - 使用预定义词汇表避免初始化问题
            # 确保max_tokens足够大以容纳预定义词汇表
            vocab_list = ['unknown', 'category_0', 'category_1', 'category_2', 'category_3', 
                         'category_4', 'category_5', 'category_6', 'category_7', 'category_8', 'category_9']
            max_tokens = max(len(vocab_list) + 2, spec.vocab_size or 100)
            lookup = tf.keras.layers.StringLookup(
                vocabulary=vocab_list, 
                mask_token=None,
                num_oov_indices=1,
                max_tokens=max_tokens
            )(input_layer)
            embedding = tf.keras.layers.Embedding(
                input_dim=spec.vocab_size or 100,
                output_dim=spec.embedding_dim or 8
            )(lookup)
            # 对嵌入进行全局平均池化，使用Lambda层包装
            expanded = tf.keras.layers.Lambda(lambda x: tf.expand_dims(x, 1))(embedding)
            pooled = tf.keras.layers.GlobalAveragePooling1D()(expanded)
            return input_layer, pooled
        
        else:  # TEXT
            input_layer = tf.keras.layers.Input(shape=(), name=spec.name, dtype=tf.string)
            # 文本特征使用更大的嵌入
            # 确保max_tokens足够大以容纳预定义词汇表
            vocab_list = ['unknown', 'text_0', 'text_1', 'text_2', 'text_3']
            max_tokens = max(len(vocab_list) + 2, spec.vocab_size or 1000)
            lookup = tf.keras.layers.StringLookup(
                vocabulary=vocab_list,
                mask_token=None, 
                num_oov_indices=1,
                max_tokens=max_tokens
            )(input_layer)
            embedding = tf.keras.layers.Embedding(
                input_dim=spec.vocab_size or 1000,
                output_dim=spec.embedding_dim or 64
            )(lookup)
            expanded = tf.keras.layers.Lambda(lambda x: tf.expand_dims(x, 1))(embedding)
            pooled = tf.keras.layers.GlobalAveragePooling1D()(expanded)
            return input_layer, pooled
    
    def _create_sequence_layers(self, spec: FeatureSpec) -> Tuple[tf.keras.layers.Input, tf.keras.layers.Layer]:
        """创建序列特征的输入和处理层"""
        
        input_layer = tf.keras.layers.Input(shape=(), name=spec.name, dtype=tf.string)
        
        # 序列处理：分词 -> 查找 -> 嵌入 -> GRU
        # 使用预定义词汇表避免初始化问题
        # 确保max_tokens足够大以容纳预定义词汇表
        vocab_list = ['unknown', 'seq_0', 'seq_1', 'seq_2', 'seq_3', 'seq_4']
        max_tokens = max(len(vocab_list) + 2, 1000)
        lookup = tf.keras.layers.StringLookup(
            vocabulary=vocab_list,
            mask_token=None,
            num_oov_indices=1,
            max_tokens=max_tokens
        )(input_layer)
        
        embedding = tf.keras.layers.Embedding(
            input_dim=1000,
            output_dim=32
        )(lookup)
        
        # 使用GRU处理序列，使用Lambda层包装
        expanded = tf.keras.layers.Lambda(lambda x: tf.expand_dims(x, 1))(embedding)
        gru_output = tf.keras.layers.GRU(32)(expanded)
        
        return input_layer, gru_output


# 全局实例
adaptive_factory = AdaptiveModelFactory()