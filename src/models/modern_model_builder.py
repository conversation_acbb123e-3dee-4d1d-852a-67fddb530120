"""
现代化模型构建器 - 整合SOTA技术的转化率预测模型

核心改进：
1. Transformer-based架构替代传统GRU
2. AutoML特征选择和重要性评估  
3. 现代化损失函数组合
4. 混合精度训练和高级优化策略
5. 多模态特征融合
"""

import tensorflow as tf
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
import logging
from dataclasses import dataclass
from sklearn.feature_selection import SelectKBest, mutual_info_classif, RFE
from sklearn.ensemble import RandomForestClassifier
import warnings

logger = logging.getLogger(__name__)


@dataclass
class ModelConfig:
    """现代化模型配置"""
    # Transformer配置
    d_model: int = 128
    num_heads: int = 8
    num_layers: int = 3
    ff_dim: int = 256
    dropout_rate: float = 0.1
    
    # 训练配置
    use_mixed_precision: bool = True
    gradient_accumulation_steps: int = 4
    use_adversarial_training: bool = False
    
    # AutoML配置
    auto_feature_selection: bool = True
    max_features: int = 200
    importance_threshold: float = 0.001
    correlation_threshold: float = 0.9
    
    # 损失函数配置
    focal_loss_alpha: float = 0.25
    focal_loss_gamma: float = 2.0
    label_smoothing_epsilon: float = 0.1
    temporal_consistency_weight: float = 0.1
    
    # 基础配置
    output_dim: int = 6
    learning_rate: float = 0.001
    
    # 网络结构
    hidden_units: List[int] = None
    use_batch_norm: bool = True
    activation: str = 'relu'
    
    # 高级组件
    use_cross_net: bool = True
    cross_layers: int = 3
    use_attention: bool = True
    attention_dim: int = 64
    
    # 正则化
    l2_reg: float = 1e-5
    embedding_l2_reg: float = 1e-6
    
    # 类别不平衡处理
    use_focal_loss: bool = True
    focal_alpha: float = 0.75
    focal_gamma: float = 2.0
    
    def __post_init__(self):
        if self.hidden_units is None:
            self.hidden_units = [512, 256, 128]


class CrossNetwork(tf.keras.layers.Layer):
    """Cross Network - DCN v2实现"""
    
    def __init__(self, num_layers: int = 3, **kwargs):
        super().__init__(**kwargs)
        self.num_layers = num_layers
        self.cross_layers = []
        
    def build(self, input_shape):
        dim = input_shape[-1]
        for i in range(self.num_layers):
            self.cross_layers.append(
                tf.keras.layers.Dense(
                    dim, 
                    use_bias=True,
                    kernel_initializer='truncated_normal',
                    name=f'cross_layer_{i}'
                )
            )
        super().build(input_shape)
        
    def call(self, x0, training=None):
        xl = x0
        for cross_layer in self.cross_layers:
            xl_w = cross_layer(xl)  # 线性变换
            xl = x0 * xl_w + xl     # 交叉操作
        return xl


class MultiHeadAttention(tf.keras.layers.Layer):
    """多头注意力机制"""
    
    def __init__(self, num_heads: int = 8, key_dim: int = 64, **kwargs):
        super().__init__(**kwargs)
        self.num_heads = num_heads
        self.key_dim = key_dim
        self.attention = tf.keras.layers.MultiHeadAttention(
            num_heads=num_heads,
            key_dim=key_dim,
            dropout=0.1
        )
        self.layer_norm = tf.keras.layers.LayerNormalization()
        
    def call(self, x, training=None):
        # 添加时间维度进行自注意力
        if len(x.shape) == 2:
            x = tf.expand_dims(x, 1)  # (batch, 1, dim)
            
        attn_output = self.attention(x, x, training=training)
        output = self.layer_norm(x + attn_output)
        
        # 移除时间维度
        if output.shape[1] == 1:
            output = tf.squeeze(output, axis=1)
            
        return output


class FocalLoss(tf.keras.losses.Loss):
    """Focal Loss for addressing class imbalance"""
    
    def __init__(self, alpha=0.25, gamma=2.0, name="focal_loss"):
        super().__init__(name=name)
        self.alpha = alpha
        self.gamma = gamma
    
    def call(self, y_true, y_pred):
        y_pred = tf.clip_by_value(y_pred, 1e-8, 1.0 - 1e-8)
        
        # 计算交叉熵
        ce_loss = -y_true * tf.math.log(y_pred) - (1 - y_true) * tf.math.log(1 - y_pred)
        
        # 计算pt
        pt = tf.where(tf.equal(y_true, 1), y_pred, 1 - y_pred)
        
        # 计算alpha_t
        alpha_t = tf.where(tf.equal(y_true, 1), self.alpha, 1 - self.alpha)
        
        # 计算focal weight
        focal_weight = alpha_t * tf.pow(1 - pt, self.gamma)
        
        focal_loss = focal_weight * ce_loss
        return tf.reduce_mean(focal_loss)


class TemporalConsistencyLoss(tf.keras.losses.Loss):
    """时间一致性损失 - 确保月度预测的时间逻辑"""
    
    def __init__(self, name="temporal_consistency_loss"):
        super().__init__(name=name)
    
    def call(self, y_true, y_pred):
        # 计算相邻月份预测值的差异
        month_diffs = y_pred[:, 1:] - y_pred[:, :-1]
        
        # 惩罚突然增长（转化率应该随时间递减）
        sudden_increase_penalty = tf.nn.relu(month_diffs)
        
        # 计算平均惩罚
        consistency_loss = tf.reduce_mean(sudden_increase_penalty)
        
        return consistency_loss


class LabelSmoothingLoss(tf.keras.losses.Loss):
    """标签平滑损失"""
    
    def __init__(self, epsilon=0.1, name="label_smoothing_loss"):
        super().__init__(name=name)
        self.epsilon = epsilon
    
    def call(self, y_true, y_pred):
        y_pred = tf.clip_by_value(y_pred, 1e-8, 1.0 - 1e-8)
        
        # 应用标签平滑
        smoothed_labels = y_true * (1 - self.epsilon) + 0.5 * self.epsilon
        
        # 计算交叉熵
        loss = -smoothed_labels * tf.math.log(y_pred) - (1 - smoothed_labels) * tf.math.log(1 - y_pred)
        
        return tf.reduce_mean(loss)


class ModernLossFunction(tf.keras.losses.Loss):
    """现代化组合损失函数"""
    
    def __init__(self, config: ModelConfig, name="modern_combined_loss"):
        super().__init__(name=name)
        self.config = config
        self.focal_loss = FocalLoss(config.focal_loss_alpha, config.focal_loss_gamma)
        self.temporal_loss = TemporalConsistencyLoss()
        self.smoothing_loss = LabelSmoothingLoss(config.label_smoothing_epsilon)
    
    def call(self, y_true, y_pred):
        # 主要损失：Focal Loss
        focal = self.focal_loss(y_true, y_pred)
        
        # 时间一致性损失
        temporal = self.temporal_loss(y_true, y_pred)
        
        # 标签平滑损失
        smoothing = self.smoothing_loss(y_true, y_pred)
        
        # 组合损失
        total_loss = focal + self.config.temporal_consistency_weight * temporal + 0.05 * smoothing
        
        return total_loss


class AutoMLFeatureSelector:
    """AutoML特征选择器"""
    
    def __init__(self, config: ModelConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.selected_features = None
        self.feature_importance = None
    
    def select_features(self, X: pd.DataFrame, y: np.ndarray) -> List[str]:
        """
        自动特征选择
        
        Args:
            X: 特征数据
            y: 标签数据
            
        Returns:
            选择的特征名列表
        """
        if not self.config.auto_feature_selection:
            return list(X.columns)
        
        self.logger.info("🧠 开始AutoML特征选择...")
        
        # 1. 移除高缺失率特征 (>80%)
        high_missing_features = X.columns[X.isnull().mean() > 0.8].tolist()
        X_cleaned = X.drop(columns=high_missing_features)
        self.logger.info(f"   移除高缺失率特征: {len(high_missing_features)} 个")
        
        # 2. 移除高相关性特征
        X_numeric = X_cleaned.select_dtypes(include=[np.number])
        if len(X_numeric.columns) > 1:
            correlation_matrix = X_numeric.corr().abs()
            upper_tri = correlation_matrix.where(
                np.triu(np.ones(correlation_matrix.shape), k=1).astype(bool)
            )
            high_corr_features = [
                column for column in upper_tri.columns 
                if any(upper_tri[column] > self.config.correlation_threshold)
            ]
            X_cleaned = X_cleaned.drop(columns=high_corr_features)
            self.logger.info(f"   移除高相关性特征: {len(high_corr_features)} 个")
        
        # 3. 基于互信息的特征重要性评估
        try:
            # 准备数值化数据
            X_prepared = self._prepare_features_for_selection(X_cleaned)
            y_binary = (y.sum(axis=1) > 0).astype(int)  # 转换为二分类问题
            
            # 使用SelectKBest进行初步筛选
            selector = SelectKBest(
                score_func=mutual_info_classif, 
                k=min(self.config.max_features, len(X_prepared.columns))
            )
            
            X_selected = selector.fit_transform(X_prepared, y_binary)
            selected_indices = selector.get_support(indices=True)
            selected_features = X_prepared.columns[selected_indices].tolist()
            
            # 记录特征重要性
            self.feature_importance = dict(zip(
                selected_features,
                selector.scores_[selected_indices]
            ))
            
            self.logger.info(f"   互信息筛选后保留: {len(selected_features)} 个特征")
            
        except Exception as e:
            self.logger.warning(f"   互信息特征选择失败: {e}, 使用原始特征")
            selected_features = list(X_cleaned.columns)
        
        # 4. 基于随机森林的递归特征消除 (如果特征仍然太多)
        if len(selected_features) > self.config.max_features:
            try:
                X_for_rfe = X_cleaned[selected_features]
                X_for_rfe_prepared = self._prepare_features_for_selection(X_for_rfe)
                
                rf = RandomForestClassifier(n_estimators=50, random_state=42, n_jobs=-1)
                rfe = RFE(rf, n_features_to_select=self.config.max_features)
                
                with warnings.catch_warnings():
                    warnings.simplefilter("ignore")
                    rfe.fit(X_for_rfe_prepared, y_binary)
                
                rfe_selected = X_for_rfe_prepared.columns[rfe.support_].tolist()
                selected_features = rfe_selected
                
                self.logger.info(f"   RFE筛选后保留: {len(selected_features)} 个特征")
                
            except Exception as e:
                self.logger.warning(f"   RFE特征选择失败: {e}, 保持当前选择")
        
        self.selected_features = selected_features
        self.logger.info(f"✅ AutoML特征选择完成，最终保留: {len(selected_features)} 个特征")
        
        return selected_features
    
    def _prepare_features_for_selection(self, X: pd.DataFrame) -> pd.DataFrame:
        """为特征选择准备数据"""
        X_prepared = X.copy()
        
        # 处理数值特征
        numeric_cols = X_prepared.select_dtypes(include=[np.number]).columns
        X_prepared[numeric_cols] = X_prepared[numeric_cols].fillna(0)
        
        # 处理类别特征 - 简单编码
        categorical_cols = X_prepared.select_dtypes(include=['object', 'category']).columns
        for col in categorical_cols:
            X_prepared[col] = pd.Categorical(X_prepared[col].fillna('unknown')).codes
        
        return X_prepared
    
    def get_feature_importance(self) -> Optional[Dict[str, float]]:
        """获取特征重要性评分"""
        return self.feature_importance


class TransformerBasedEncoder(tf.keras.layers.Layer):
    """基于Transformer的特征编码器"""
    
    def __init__(self, config: ModelConfig, **kwargs):
        super().__init__(**kwargs)
        self.config = config
        
        # Transformer层
        self.transformer_layers = [
            tf.keras.layers.MultiHeadAttention(
                num_heads=config.num_heads,
                key_dim=config.d_model // config.num_heads,
                dropout=config.dropout_rate
            ) for _ in range(config.num_layers)
        ]
        
        # Feed-forward层
        self.ffn_layers = [
            tf.keras.Sequential([
                tf.keras.layers.Dense(config.ff_dim, activation='relu'),
                tf.keras.layers.Dropout(config.dropout_rate),
                tf.keras.layers.Dense(config.d_model)
            ]) for _ in range(config.num_layers)
        ]
        
        # 层归一化
        self.layer_norms1 = [
            tf.keras.layers.LayerNormalization(epsilon=1e-6) 
            for _ in range(config.num_layers)
        ]
        self.layer_norms2 = [
            tf.keras.layers.LayerNormalization(epsilon=1e-6) 
            for _ in range(config.num_layers)
        ]
        
        # 全局池化
        self.global_pool = tf.keras.layers.GlobalAveragePooling1D()
        
    def call(self, inputs, training=None):
        x = inputs
        
        # 通过Transformer层
        for i in range(self.config.num_layers):
            # Multi-head attention
            attn_output = self.transformer_layers[i](x, x, training=training)
            x = self.layer_norms1[i](x + attn_output)
            
            # Feed-forward
            ffn_output = self.ffn_layers[i](x, training=training)
            x = self.layer_norms2[i](x + ffn_output)
        
        # 全局池化
        return self.global_pool(x)


class ModernModelBuilder:
    """现代化模型构建器"""
    
    def __init__(self, config: ModelConfig = None):
        self.config = config or ModelConfig()
        self.logger = logging.getLogger(__name__)
        self.feature_selector = AutoMLFeatureSelector(self.config)
        
        # 启用混合精度训练
        if self.config.use_mixed_precision:
            policy = tf.keras.mixed_precision.Policy('mixed_float16')
            tf.keras.mixed_precision.set_global_policy(policy)
            self.logger.info("✅ 混合精度训练已启用")
    
    def build_model(self, 
                    feature_specs: Dict[str, Any],
                    output_dim: int = 6) -> tf.keras.Model:
        """
        构建现代化模型
        
        Args:
            feature_specs: 特征规格
            output_dim: 输出维度
            
        Returns:
            编译好的Keras模型
        """
        self.logger.info("🚀 构建现代化Transformer模型...")
        
        # 构建输入层
        inputs, embeddings = self._build_input_layers(feature_specs)
        
        # 特征融合
        if embeddings:
            # 合并所有嵌入
            merged_features = tf.keras.layers.Concatenate()(embeddings)
            
            # 投影到Transformer维度
            projected = tf.keras.layers.Dense(
                self.config.d_model,
                activation='relu',
                name='feature_projection'
            )(merged_features)
            
            # 扩展维度以适配Transformer
            sequence_input = tf.keras.layers.Lambda(
                lambda x: tf.expand_dims(x, 1)
            )(projected)
            
            # Transformer编码器
            transformer_encoder = TransformerBasedEncoder(self.config)
            encoded_features = transformer_encoder(sequence_input)
            
        else:
            # 如果没有特征，创建虚拟输入
            dummy_input = tf.keras.layers.Input(shape=(1,), name='dummy')
            inputs['dummy'] = dummy_input
            encoded_features = dummy_input
        
        # 预测塔
        x = encoded_features
        
        # 深度网络
        x = tf.keras.layers.Dense(256, activation='relu')(x)
        x = tf.keras.layers.BatchNormalization()(x)
        x = tf.keras.layers.Dropout(0.3)(x)
        
        x = tf.keras.layers.Dense(128, activation='relu')(x)
        x = tf.keras.layers.BatchNormalization()(x)
        x = tf.keras.layers.Dropout(0.2)(x)
        
        x = tf.keras.layers.Dense(64, activation='relu')(x)
        x = tf.keras.layers.BatchNormalization()(x)
        x = tf.keras.layers.Dropout(0.1)(x)
        
        # 输出层
        outputs = tf.keras.layers.Dense(
            output_dim,
            activation='sigmoid',
            name='predictions'
        )(x)
        
        # 创建模型
        model = tf.keras.Model(inputs=inputs, outputs=outputs)
        
        # 编译模型
        self._compile_model(model)
        
        self.logger.info(f"✅ 现代化模型构建完成，参数数量: {model.count_params():,}")
        
        return model
    
    def _build_network_architecture(self, merged_features: tf.Tensor) -> tf.Tensor:
        """构建网络架构"""
        x = merged_features
        
        # Dense层归一化
        x = tf.keras.layers.LayerNormalization(name='input_norm')(x)
        
        # Cross Network分支
        if self.config.use_cross_net:
            cross_output = CrossNetwork(
                num_layers=self.config.cross_layers,
                name='cross_network'
            )(x)
        else:
            cross_output = x
        
        # Deep Network分支
        deep_output = self._build_deep_network(x)
        
        # 注意力机制
        if self.config.use_attention:
            attention_output = MultiHeadAttention(
                num_heads=8,
                key_dim=self.config.attention_dim,
                name='multi_head_attention'
            )(deep_output)
            deep_output = attention_output
        
        # 合并Cross和Deep
        if self.config.use_cross_net:
            combined = tf.keras.layers.Concatenate(name='cross_deep_concat')([cross_output, deep_output])
        else:
            combined = deep_output
        
        # 输出层
        output = self._build_output_layer(combined)
        
        return output
    
    def _build_deep_network(self, x: tf.Tensor) -> tf.Tensor:
        """构建深度网络"""
        for i, units in enumerate(self.config.hidden_units):
            # Dense层
            x = tf.keras.layers.Dense(
                units,
                kernel_regularizer=tf.keras.regularizers.l2(self.config.l2_reg),
                name=f'dense_{i}'
            )(x)
            
            # 批归一化
            if self.config.use_batch_norm:
                x = tf.keras.layers.BatchNormalization(name=f'bn_{i}')(x)
            
            # 激活函数
            x = tf.keras.layers.Activation(self.config.activation, name=f'activation_{i}')(x)
            
            # Dropout
            x = tf.keras.layers.Dropout(self.config.dropout_rate, name=f'dropout_{i}')(x)
        
        return x
    
    def _build_output_layer(self, x: tf.Tensor) -> tf.Tensor:
        """构建输出层"""
        # 最终投影层
        x = tf.keras.layers.Dense(
            64,
            activation='relu',
            kernel_regularizer=tf.keras.regularizers.l2(self.config.l2_reg),
            name='final_projection'
        )(x)
        
        # 输出层
        output = tf.keras.layers.Dense(
            self.config.output_dim,
            activation='sigmoid',
            name='prediction'
        )(x)
        
        return output
    
    def _build_input_layers(self, feature_specs: Dict[str, Any]) -> Tuple[Dict, List]:
        """构建输入层"""
        inputs = {}
        embeddings = []
        
        for feature_name, spec in feature_specs.items():
            try:
                input_layer, embedding = self._create_feature_input(feature_name, spec)
                inputs[feature_name] = input_layer
                if embedding is not None:
                    embeddings.append(embedding)
            except Exception as e:
                self.logger.warning(f"跳过特征 {feature_name}: {e}")
                continue
        
        return inputs, embeddings
    
    def _create_feature_input(self, feature_name: str, spec: Any):
        """创建单个特征的输入层"""
        if hasattr(spec, 'feature_type'):
            # 新的FeatureSpec格式
            feature_type = spec.feature_type.value
            
            if feature_type == 'numerical':
                input_layer = tf.keras.layers.Input(shape=(), name=feature_name, dtype=tf.float32)
                normalized = tf.keras.layers.Lambda(lambda x: tf.expand_dims(x, -1))(input_layer)
                normalized = tf.keras.layers.BatchNormalization()(normalized)
                return input_layer, normalized
                
            elif feature_type in ['categorical', 'text']:
                input_layer = tf.keras.layers.Input(shape=(), name=feature_name, dtype=tf.string)
                
                # 使用预定义词汇表
                vocab_size = getattr(spec, 'vocab_size', 1000)
                embedding_dim = getattr(spec, 'embedding_dim', 64)
                
                vocab_list = [f'token_{i}' for i in range(min(100, vocab_size))]
                lookup = tf.keras.layers.StringLookup(
                    vocabulary=vocab_list,
                    mask_token=None,
                    num_oov_indices=1
                )(input_layer)
                
                embedding = tf.keras.layers.Embedding(
                    input_dim=vocab_size,
                    output_dim=embedding_dim
                )(lookup)
                
                pooled = tf.keras.layers.GlobalAveragePooling1D()(
                    tf.keras.layers.Lambda(lambda x: tf.expand_dims(x, 1))(embedding)
                )
                
                return input_layer, pooled
                
            elif feature_type == 'sequence':
                input_layer = tf.keras.layers.Input(shape=(), name=feature_name, dtype=tf.string)
                
                # 序列处理
                vocab_list = [f'seq_token_{i}' for i in range(100)]
                lookup = tf.keras.layers.StringLookup(
                    vocabulary=vocab_list,
                    mask_token=None,
                    num_oov_indices=1
                )(input_layer)
                
                embedding = tf.keras.layers.Embedding(
                    input_dim=1000,
                    output_dim=64
                )(lookup)
                
                # 使用GRU处理序列
                gru_output = tf.keras.layers.GRU(64)(
                    tf.keras.layers.Lambda(lambda x: tf.expand_dims(x, 1))(embedding)
                )
                
                return input_layer, gru_output
        
        else:
            # 旧的字典格式
            feature_type = spec.get('type', 'table')
            feature_dtype = spec.get('dtype', 'StringLookup')
            
            if feature_dtype == 'Dense' or feature_dtype == 'Bucket':
                input_layer = tf.keras.layers.Input(shape=(), name=feature_name, dtype=tf.float32)
                normalized = tf.keras.layers.Lambda(lambda x: tf.expand_dims(x, -1))(input_layer)
                return input_layer, normalized
            else:
                input_layer = tf.keras.layers.Input(shape=(), name=feature_name, dtype=tf.string)
                
                vocab_list = [f'cat_{i}' for i in range(100)]
                lookup = tf.keras.layers.StringLookup(
                    vocabulary=vocab_list,
                    mask_token=None,
                    num_oov_indices=1
                )(input_layer)
                
                embedding = tf.keras.layers.Embedding(
                    input_dim=1000,
                    output_dim=32
                )(lookup)
                
                pooled = tf.keras.layers.GlobalAveragePooling1D()(
                    tf.keras.layers.Lambda(lambda x: tf.expand_dims(x, 1))(embedding)
                )
                
                return input_layer, pooled
    
    def _compile_model(self, model: tf.keras.Model):
        """编译模型"""
        # 现代化优化器
        optimizer = tf.keras.optimizers.AdamW(
            learning_rate=1e-3,
            weight_decay=1e-4,
            clipnorm=1.0
        )
        
        # 混合精度调整
        if self.config.use_mixed_precision:
            optimizer = tf.keras.mixed_precision.LossScaleOptimizer(optimizer)
        
        # 现代化损失函数
        loss_fn = ModernLossFunction(self.config)
        
        # 编译
        model.compile(
            optimizer=optimizer,
            loss=loss_fn,
            metrics=[
                'accuracy',
                tf.keras.metrics.Precision(name='precision'),
                tf.keras.metrics.Recall(name='recall'),
                tf.keras.metrics.AUC(name='auc'),
                tf.keras.metrics.AUC(curve='PR', name='pr_auc')
            ]
        )
        
        self.logger.info("✅ 模型编译完成，使用现代化损失函数和优化器")
    
    def create_callbacks(self, output_dir: str) -> List[tf.keras.callbacks.Callback]:
        """创建现代化训练回调"""
        callbacks = []
        
        # 早停
        callbacks.append(
            tf.keras.callbacks.EarlyStopping(
                monitor='val_pr_auc',
                patience=5,
                mode='max',
                restore_best_weights=True,
                verbose=1
            )
        )
        
        # 学习率调度
        callbacks.append(
            tf.keras.callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=3,
                min_lr=1e-6,
                verbose=1
            )
        )
        
        # 模型检查点
        callbacks.append(
            tf.keras.callbacks.ModelCheckpoint(
                filepath=f"{output_dir}/best_model.h5",
                monitor='val_pr_auc',
                mode='max',
                save_best_only=True,
                verbose=1
            )
        )
        
        # TensorBoard
        callbacks.append(
            tf.keras.callbacks.TensorBoard(
                log_dir=f"{output_dir}/tensorboard",
                histogram_freq=1,
                write_graph=True
            )
        )
        
        return callbacks


def create_dataset_from_pipeline(data: Dict[str, tf.Tensor], 
                                labels: tf.Tensor,
                                batch_size: int = 1024,
                                shuffle: bool = True) -> tf.data.Dataset:
    """从特征管道输出创建TensorFlow数据集"""
    # 创建数据集
    dataset = tf.data.Dataset.from_tensor_slices((data, labels))
    
    if shuffle:
        # 根据数据大小调整缓冲区大小，避免过长的填充时间
        data_size = len(list(data.values())[0])
        buffer_size = min(data_size, 5000)  # 最大5000，避免内存问题
        dataset = dataset.shuffle(buffer_size=buffer_size)
    
    dataset = dataset.batch(batch_size)
    dataset = dataset.prefetch(tf.data.AUTOTUNE)
    
    return dataset


# 全局实例
modern_model_builder = ModernModelBuilder()