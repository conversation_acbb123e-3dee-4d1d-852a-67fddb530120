# NIO转化率预测模型优化规划路线图

## 📊 现状评估

### 当前性能基线
- **验证准确率**: 13.0% (最佳29.0%)
- **模型收敛**: 稳定，无过拟合
- **训练效率**: 29秒/15epochs
- **架构状态**: 自适应构建，参数147万

### 核心问题诊断
1. **特征冗余严重**: 搜索行为7时间窗口相关性0.7-0.93
2. **缺失值处理粗糙**: 重要特征73%缺失简单填充
3. **不平衡学习不足**: 4%正样本率未充分优化
4. **评估指标不当**: accuracy对稀有事件不敏感

## 🎯 分阶段优化规划

### 📋 **阶段一：特征工程优化** (预期提升: +10~15%)

#### 1.1 特征去冗余 (优先级: 🔥)
**目标**: 移除高相关特征，提升学习效率
```python
# 高相关特征处理策略
redundant_groups = {
    'search_intention': {
        'features': ['cnt_1d', 'cnt_7d', 'cnt_14d', 'cnt_30d', 'cnt_60d', 'cnt_90d', 'cnt_180d'],
        'keep': ['cnt_30d', 'cnt_90d'],  # 保留30天和90天
        'reason': '相关性0.7-0.93，保留关键时间窗口'
    },
    'login_behavior': {
        'features': ['login_30d', 'login_60d', 'login_90d', 'login_180d'],
        'keep': ['login_30d', 'login_180d'],  # 保留短期和长期
        'reason': '避免中等时间窗口冗余'
    }
}
```

#### 1.2 缺失值策略优化 (优先级: 🔥)
**目标**: 针对重要特征设计业务逻辑填充
```python
# 重要特征缺失值处理
missing_strategies = {
    'app_search_intention_DSLA': {
        'current_missing': 73.2,
        'strategy': 'business_encoding',  # 缺失本身是特征
        'implementation': 'create_missing_indicator + median_fill'
    },
    'user_core_*_reg_leads_nio_DSLA': {
        'current_missing': 37-60,
        'strategy': 'time_based_fill',
        'implementation': 'use_user_register_days_as_baseline'
    }
}
```

#### 1.3 类别特征重编码 (优先级: 📊)
**目标**: 降低稀疏性，提升embedding效果
```python
# 地理特征分层编码
city_encoding = {
    'tier1_cities': ['上海市', '北京市', '深圳市', '广州市'],  # 8.6%
    'tier2_cities': ['杭州市', '成都市', '南京市', '武汉市'],
    'other_cities': 'aggregate_long_tail'  # 剩余400+城市
}

# 职业特征合并
career_encoding = {
    'known_careers': 'top_10_careers',  # 85.7%
    'unknown_none': 'merge_unknown_and_none'  # 14.3%
}
```

### 📋 **阶段二：损失函数与采样优化** (预期提升: +5~8%)

#### 2.1 Focal Loss实施 (优先级: 🔥)
**目标**: 专门处理4%正样本率的不平衡
```python
# Focal Loss配置
focal_loss_config = {
    'alpha': 0.75,  # 正样本权重
    'gamma': 2.0,   # 困难样本聚焦参数
    'label_smoothing': 0.1,
    'expected_improvement': '+3~5% accuracy'
}
```

#### 2.2 评估指标优化 (优先级: 📊)
**目标**: 使用业务相关的评估指标
```python
# 新评估指标体系
evaluation_metrics = {
    'primary': 'PR-AUC',  # 主要指标
    'secondary': ['Precision@K', 'Recall@K', 'F1-Score'],
    'business': ['首月预测准确率', '转化漏斗分析'],
    'remove': 'accuracy'  # 移除misleading指标
}
```

### 📋 **阶段三：模型架构微调** (预期提升: +3~5%)

#### 3.1 Embedding维度优化
**目标**: 针对不同稀疏度特征调整embedding
```python
# 动态embedding维度
embedding_strategy = {
    'high_cardinality': {  # 如居住城市409值
        'dimension': 16,
        'regularization': 'L2(0.01)'
    },
    'medium_cardinality': {  # 如职业24值
        'dimension': 8,
        'regularization': 'L2(0.001)'
    },
    'low_cardinality': {  # 如性别3值
        'dimension': 4,
        'regularization': None
    }
}
```

#### 3.2 时间衰减权重精细化
**目标**: 更好地建模月度转化率递减(1.29%→0.38%)
```python
# 时间衰减策略
time_decay_weights = {
    'month_1': 1.0,     # 基准权重
    'month_2': 0.8,     # 对应实际比例
    'month_3': 0.65,
    'month_4': 0.7,     # 略有回升
    'month_5': 0.5,
    'month_6': 0.3      # 最低权重
}
```

### 📋 **阶段四：训练策略优化** (预期提升: +2~4%)

#### 4.1 学习率调度优化
```python
# 基于验证性能的学习率策略
lr_schedule = {
    'initial_lr': 0.001,
    'warm_up_epochs': 3,
    'plateau_patience': 3,
    'reduce_factor': 0.5,
    'min_lr': 1e-6,
    'early_stopping_patience': 5
}
```

#### 4.2 数据增强策略
```python
# 针对序列特征的增强
data_augmentation = {
    'sequence_noise': 0.1,      # 序列特征噪声
    'feature_dropout': 0.05,    # 特征随机丢弃
    'label_smoothing': 0.1      # 标签平滑
}
```

## 🚀 实施计划与时间安排

### Week 1-2: 特征工程优化
- [ ] **Day 1-3**: 特征相关性分析和去冗余实施
- [ ] **Day 4-7**: 缺失值策略重设计
- [ ] **Day 8-10**: 类别特征重编码
- [ ] **Day 11-14**: 特征工程效果验证

### Week 3: 损失函数和评估优化  
- [ ] **Day 15-17**: Focal Loss实施和调参
- [ ] **Day 18-19**: 新评估指标体系建立
- [ ] **Day 20-21**: A/B测试对比验证

### Week 4: 模型架构微调
- [ ] **Day 22-24**: Embedding维度优化
- [ ] **Day 25-26**: 时间衰减权重调整
- [ ] **Day 27-28**: 整体架构效果验证

## 📊 预期效果与验证标准

### 性能目标
| 优化阶段 | 当前基线 | 预期目标 | 验证指标 |
|----------|----------|----------|----------|
| **特征优化后** | 13%→29% | 25%→40% | PR-AUC, Precision@K |
| **损失优化后** | 25%→40% | 30%→45% | F1-Score, 首月准确率 |
| **架构优化后** | 30%→45% | 35%→50% | 业务指标, 泛化能力 |

### 质量标准
- **稳定性**: 多次训练结果方差<5%
- **泛化性**: 在5月数据上性能不下降>10%
- **效率性**: 训练时间增加<50%
- **可解释性**: 特征重要性符合业务逻辑

## 🔧 技术实施清单

### 立即可执行 (Week 1)
1. **特征相关性矩阵计算**
   ```bash
   python scripts/feature_correlation_analysis.py
   ```

2. **特征去冗余脚本**
   ```bash
   python scripts/feature_redundancy_removal.py --correlation_threshold=0.8
   ```

3. **缺失值分析**
   ```bash
   python scripts/missing_value_strategy.py --generate_business_rules
   ```

### 需要开发 (Week 1-2)
1. **业务规则缺失值填充器**
2. **地理特征分层编码器**  
3. **Focal Loss损失函数**
4. **PR-AUC评估器**

### 实验验证 (Week 3-4)
1. **渐进式A/B测试**
2. **跨时间窗口验证**
3. **业务指标对齐测试**

## 💡 关键成功因素

### 数据层面
- **保持特征业务语义**: 优化时不破坏领域知识
- **验证数据质量**: 确保清洗过程不引入bias
- **时间一致性**: 保证训练测试时间逻辑正确

### 模型层面  
- **渐进式优化**: 每次改进一个方面，便于问题定位
- **充分验证**: 每个改进都要在多个指标上验证
- **版本管理**: 保持每个版本的可复现性

### 业务层面
- **指标对齐**: 确保技术指标与业务价值一致
- **解释性保证**: 保持模型决策的可解释性
- **实用性验证**: 在实际业务场景中验证效果

---

**规划制定时间**: 2025-06-14  
**预计完成时间**: 2025-07-12  
**责任团队**: 机器学习建模团队  
**审批状态**: ✅ 待实施