# 配置管理规范 📋

## 🎯 概述

本文档定义了NIO转化率预测模型的配置管理规范，确保实验探索过程有序、最优方案稳定。

## 🏗️ 目录结构

```
src/configs/models/
├── GOLDEN_CONFIG.json          # 🏆 当前最优配置
├── experiments/                # 🧪 进行中的实验
│   ├── experiment_A.json
│   └── experiment_B.json
├── archived/                   # 📁 归档配置
│   ├── golden_backup_*.json    # Golden config备份
│   ├── failed_*.json          # 失败实验归档
│   └── experiment_*.json      # 成功实验归档
└── legacy/                     # 🗃️ 历史遗留配置（待清理）
```

## 🔄 实验流程

### 1. 标准实验流程

```bash
# 1. 创建实验配置（基于golden config副本）
python scripts/experiment_manager.py create --name feature_interaction_v1 \
  --description "探索特征交互组合效果"

# 2. 编辑实验配置
# 修改 src/configs/models/experiments/feature_interaction_v1.json

# 3. 运行实验
python scripts/experiment_manager.py run --name feature_interaction_v1 --epochs 3

# 4. 自动对比结果，如果更优则提示是否提升为Golden Config
# 5. 清理失败的实验配置
python scripts/experiment_manager.py cleanup
```

### 2. 手动管理流程

```bash
# 查看当前状态
python scripts/experiment_manager.py list

# 直接使用golden config训练
python src/train.py --model_code=GOLDEN_CONFIG --epochs 3

# 对比不同配置性能
python scripts/compare_models.py --config1 GOLDEN_CONFIG --config2 experiment_name
```

## 📊 Golden Config机制

### Golden Config特征
- **唯一性**: 系统中只有一个Golden Config
- **元数据**: 包含完整的性能指标和版本信息
- **可追溯**: 记录来源配置和历史变更
- **验证状态**: 标记为VALIDATED的配置

### 元数据结构
```json
{
  "_metadata": {
    "version": "v1.6_top25_features",
    "creation_date": "2025-06-16", 
    "performance": {
      "month_1_pr_auc": 0.2426,
      "month_1_recall_840": 0.5714,
      "month_1_roc_auc": 0.8691
    },
    "source_config": "enhanced_top25_features.json",
    "description": "Current best performing configuration",
    "last_updated": "2025-06-16T07:30:00Z",
    "validation_status": "VALIDATED"
  }
}
```

## 🧪 实验配置规范

### 命名规范
- **描述性**: `feature_interaction_v1`, `sequence_optimization`, `focal_loss_tuning`
- **版本化**: 使用v1, v2等后缀区分迭代
- **避免**: `test`, `new`, `final`等模糊名称

### 实验类型分类
1. **特征工程**: `feature_*`
2. **架构优化**: `arch_*`  
3. **参数调优**: `param_*`
4. **损失函数**: `loss_*`
5. **数据处理**: `data_*`

### 实验元数据
```json
{
  "_experiment_metadata": {
    "experiment_name": "feature_interaction_v1",
    "description": "探索意向×搜索特征交互",
    "created_from": "v1.6_top25_features",
    "created_at": "2025-06-16T08:00:00Z",
    "status": "CREATED"
  }
}
```

## 🎯 性能评估标准

### 提升阈值
- **Month_1 PR_AUC**: 提升 > 0.001 (0.1%)
- **Month_1 Recall@840**: 提升 > 0.01 (1%)
- **训练稳定性**: 收敛epoch数 < 5

### 决策流程
1. **显著提升** (>1%): 自动提示提升为Golden
2. **微小提升** (0.1%-1%): 需要人工确认
3. **无提升/下降**: 自动归档到failed

## 🧹 清理策略

### 自动清理
- 失败实验配置 → `archived/failed_*`
- 成功但未采用实验 → `archived/experiment_*`
- Golden Config备份 → `archived/golden_backup_*`

### 手动清理
- 定期清理`legacy/`目录下的历史配置
- 归档超过30天的实验配置
- 保留最近10个Golden Config备份

## 📝 最佳实践

### 1. 实验前准备
- ✅ 明确实验目标和假设
- ✅ 基于Golden Config创建副本
- ✅ 编写清晰的实验描述
- ✅ 确保数据和环境一致

### 2. 实验执行
- ✅ 使用标准化的训练参数(3 epochs)
- ✅ 记录实验过程和观察
- ✅ 及时对比性能指标
- ✅ 保存完整的实验日志

### 3. 实验后处理
- ✅ 及时决策是否采用新配置
- ✅ 归档或清理实验配置
- ✅ 更新实验记录文档
- ✅ 分享经验和洞察

### 4. 避免的做法
- ❌ 直接修改Golden Config
- ❌ 保留大量未命名的实验配置
- ❌ 忽略实验失败的原因分析
- ❌ 不及时清理冗余配置

## 🔧 工具使用

### experiment_manager.py 命令
```bash
# 创建实验
python scripts/experiment_manager.py create --name my_experiment --description "实验描述"

# 运行实验  
python scripts/experiment_manager.py run --name my_experiment --epochs 3

# 列出状态
python scripts/experiment_manager.py list

# 清理配置
python scripts/experiment_manager.py cleanup
```

### 集成到CLAUDE.md
更新项目的CLAUDE.md文件，添加配置管理相关命令：

```bash
# 使用Golden Config训练
python src/train.py --model_code=GOLDEN_CONFIG --epochs 3

# 创建并测试新实验
python scripts/experiment_manager.py create --name my_idea
# 编辑 src/configs/models/experiments/my_idea.json
python scripts/experiment_manager.py run --name my_idea
```

## 📈 版本历史

| 版本 | 日期 | PR_AUC | 变更说明 |
|------|------|---------|----------|
| v1.6_top25_features | 2025-06-16 | 0.2426 | 精简为35个核心特征 |
| v1.5_final_test | 2025-06-16 | 0.2235 | 40特征平衡版本 |
| v1.4_high_recall | 2025-06-15 | 0.1894 | 53特征高召回优化 |

## 🚀 下一步行动

1. **清理legacy配置**: 将现有20+个配置文件整理到新结构
2. **建立监控**: 跟踪Golden Config性能趋势
3. **自动化测试**: 集成CI/CD流程验证配置有效性
4. **团队培训**: 确保团队成员了解新的配置管理流程