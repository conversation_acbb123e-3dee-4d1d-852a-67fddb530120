# NIO转化率预测模型迭代经验积累

## 📊 迭代历程总览

### 版本演进路径
```
初始版本(0.03 PR_AUC) → 多任务学习(0.07 PR_AUC) → 历史复现(0.16 PR_AUC) → 关键特征优化(0.19 PR_AUC)
```

## 🎯 关键迭代经验

### 第一轮：多任务学习探索（失败→成功）
**时间**：2025-06-15  
**问题**：维度不匹配错误  
**根因**：配置文件中`use_multitask=true`但FeatureBuilder接收到False  
**解决方案**：修复train.py中参数传递逻辑
```python
# 修复前：硬编码判断
use_multitask = True if args.model_code == "multitask_test" else False

# 修复后：从配置文件读取
use_multitask = model_config.get("use_multitask", False)
```
**效果**：PR_AUC从0.03提升到0.07（2.3倍提升）  
**教训**：参数传递逻辑要仔细检查，配置文件与代码要保持一致

### 第二轮：历史配置复现（重大突破）
**时间**：2025-06-15  
**问题**：过度追求"现代化"架构，忽略了经典配置的价值  
**关键发现**：
- 使用原始`EPMMOENet`而非`EPMMOENet_Enhanced`
- 关闭多任务学习（`use_multitask: false`）  
- 经典focal loss参数：`alpha=0.35, gamma=2.5, pos_weight=10.0`
- 月份权重[5.0, 2.0, 2.0, 2.0, 1.0, 1.0]是关键

**效果**：PR_AUC从0.07提升到0.16（2.3倍提升）  
**教训**：**不要盲目创新，要先理解和复现历史成功经验**

### 第三轮：Focal Loss参数优化（效果有限）
**时间**：2025-06-15  
**尝试**：alpha=0.35→0.6, gamma=2.5→1.8, pos_weight=10→15  
**效果**：PR_AUC从0.16下降到0.15（-6%）  
**教训**：**原始参数可能已接近最优，过度调参适得其反**

### 第四轮：关键特征工程（重大突破）⭐
**时间**：2025-06-15  
**核心发现**：通过数据分析发现遗漏了重要性排名前10的关键特征

#### 缺失的关键特征及其重要性
| 特征名 | 重要性 | 业务含义 | 购买漏斗对应 |

### 第五轮：最终特征整合（历史性突破）🚀
**时间**：2025-06-16  
**配置文件**：`enhanced_final_test.json`  
**核心策略**：将所有关键特征整合到一个稳定配置中

#### 最终配置特点
- **40个精选特征**：涵盖购买漏斗全流程
- **经典架构**：`EPMMOENet`（非Enhanced版本）
- **优化参数**：`focal_alpha=0.4, focal_gamma=2.0, pos_weight=20.0`
- **无序列特征**：避免数据类型兼容性问题

#### 突破性结果
- **Month_1 PR_AUC**: 0.2235 ⬆️ (相比前版本0.1894提升18%)
- **Month_1 Recall@840**: 57.14% ⬆️ (相比55.46%提升)
- **稳定收敛**：3个epoch即达到最佳效果

#### 关键技术决策
1. **特征整合**：合并高价值特征而非盲目增加数量
2. **架构选择**：原始EPMMOENet比Enhanced版本更稳定
3. **参数调优**：适度提高pos_weight到20.0增强recall
4. **评估修复**：时间戳目录结构确保结果整洁记录

**历史性意义**：**首次突破0.22 PR_AUC**，接近历史最佳的0.55目标

### 第六轮：特征精简优化（再次突破）🎖️
**时间**：2025-06-16  
**核心发现**：**特征质量胜过数量** - 精选特征比全量特征效果更好

#### 实验对比分析
| 配置版本 | 特征数量 | Month_1 PR_AUC | 变化幅度 | 核心洞察 |
|---------|---------|----------------|---------|----------|
| enhanced_final_test | 40特征 | 0.2235 | 基准 | 🎯 平衡版本 |
| enhanced_plus_features | 53特征 | 0.1884 | -15.7% ⬇️ | ❌ 特征过载 |
| enhanced_top25_features | 35特征 | **0.2426** | **+8.5%** ⬆️ | ✅ **精简制胜** |

#### 关键技术发现
1. **特征选择策略**：只保留TOP 25重要性特征 + 10个关键分类特征
2. **噪音特征剔除**：移除重要性<1%的边际特征
3. **维度优化**：35个精选特征达到最佳效果

#### 突破性结果 🏆
- **Month_1 PR_AUC**: 0.2426 (目前最高记录!)
- **Month_1 Recall@840**: 57.14% (维持高水平)
- **Month_1 ROC_AUC**: 0.8691
- **训练效率**: 4.39秒 (最快收敛)

#### 核心特征构成
**数值特征 (25个)**：
- 意向相关: intention_intention_fail_days, intention_opportunity_create_days
- 搜索行为: app_search_intention_DSLA  
- 用户活跃度: user_core_visit_nioapp_login_*系列
- 线索质量: user_core_unfirst_reg_leads_nio_*系列

**分类特征 (10个)**：
- 用户画像: gender, age_group, resident_city, career_type
- 意向状态: intention_stage, intention_status
- 同伴跟进: fellow_follow_*系列

**深刻启示**：
- ✅ **少而精胜过多而杂**：35 > 53特征
- ✅ **特征重要性阈值**：保留>1%重要性特征  
- ✅ **避免特征维度诅咒**：过多特征导致稀疏性问题
- ✅ **质量至上原则**：特征选择比特征工程更重要
|--------|--------|----------|-------------|
| `intention_intention_fail_days` | 10.98% | 意向失败天数 | **意向阶段** |
| `app_search_intention_DSLA` | 9.16% | 搜索意向距离 | **搜索阶段** |
| `intention_opportunity_create_days` | 8.51% | 机会创建天数 | **意向阶段** |
| `user_core_nio_value` | 4.57% | 用户价值评分 | **用户画像** |

**效果**：PR_AUC从0.16提升到**0.19**（+18.9%提升）  
**Month_1 Recall@840**：达到**55.46%**

## 💡 核心经验总结

### 1. 特征工程是性能提升的关键
- **数据驱动选择**：基于特征重要性分析，而非主观判断
- **业务逻辑对应**：特征要与购买漏斗"意向→搜索→试驾→购车"对应
- **缺失特征影响巨大**：少了几个关键特征，性能大幅下降

### 2. 配置管理的重要性
- **参数传递链条**：配置文件→train.py→FeatureBuilder→模型，每一环都要检查
- **版本控制**：每个实验都要有明确的配置文件记录
- **向后兼容**：修改代码时要考虑历史配置的兼容性

### 3. 调参策略
- **先复现，再优化**：不要急于创新，先理解历史成功方案
- **渐进式改进**：一次只改一个因素，观察效果
- **数据驱动决策**：基于客观分析而非主观猜测

### 4. 评估指标的业务意义
- **PR_AUC vs ROC_AUC**：极不平衡场景下PR_AUC更关键
- **Recall的业务价值**：不能漏掉潜在购车用户
- **@K指标**：更符合实际推荐场景

## 🔍 下一步特征挖掘方向

### 1. 基于数据分析的特征扩展
根据`/data/analysis/reports/`的分析结果：

#### 高价值特征候选
```python
# 从feature_importance.csv排名5-20的特征
候选特征 = [
    "user_core_unfirst_reg_leads_nio_DSLA",      # 排名#5, 6.22%
    "user_core_first_reg_leads_nio_DSLA",        # 排名#6, 5.98%
    "user_core_visit_nioapp_login_180d_cnt",     # 排名#9, 5.02%
    "user_core_visit_nioapp_login_90d_cnt",      # 排名#11, 3.60%
    "user_core_unfirst_reg_leads_nio_180d_cnt",  # 排名#12, 3.04%
]
```

#### 特征组合策略
1. **时间窗口特征优化**
   - 当前使用1d, 30d，但重要性分析显示DSLA(距离天数)更重要
   - 考虑用DSLA替代部分时间窗口计数特征

2. **搜索行为特征深化**
   - 已加入`app_search_intention_DSLA`（排名#3）
   - 考虑加入`app_search_intention_cnt_180d`等长期搜索行为

3. **用户价值特征强化**
   - 已加入`user_core_nio_value`（排名#10）
   - 考虑信用额度、车辆拥有情况等财务相关特征

### 2. 特征交互和组合
```python
# 潜在的特征交互
特征交互方向 = {
    "意向×搜索": "intention_stage × app_search_intention_DSLA",
    "价值×行为": "user_core_nio_value × visit_frequency",
    "时间×意向": "intention_create_time_days × intention_status"
}
```

### 3. 缺失值处理优化
根据数据分析，`app_search_intention_*`系列缺失73.2%，需要：
- 区分"真实缺失"vs"业务缺失"（用户无搜索行为）
- 为无搜索行为用户设计专门编码

## 📋 实验模板

### 标准实验流程
1. **特征分析**：查看`/data/analysis/reports/feature_importance.csv`
2. **配置创建**：基于模板创建新的配置文件
3. **基线对比**：与当前最优版本(`high_recall_optimized`)对比
4. **结果记录**：更新本文档

### 配置文件命名规范
```
src/configs/models/
├── feature_exp_v{N}_{description}.json  # 特征实验
├── loss_exp_v{N}_{description}.json     # 损失函数实验  
├── arch_exp_v{N}_{description}.json     # 架构实验
```

### 实验记录模板
```markdown
#### 实验X：{实验名称}
**时间**：{日期}
**假设**：{实验假设}
**变更**：{具体变更内容}
**配置**：{配置文件名}
**结果**：
- Month_1 PR_AUC: {数值} ({变化})
- Month_1 Recall@840: {数值} ({变化})
**分析**：{结果分析}
**下一步**：{后续方向}
```

## 🎯 当前最优配置记录

### 最佳版本：high_recall_optimized
- **Month_1 PR_AUC**: 0.1894
- **Month_1 Recall@840**: 0.5546  
- **Month_1 ROC_AUC**: 0.8671
- **特征数量**: 53个（包含关键意向特征）
- **配置文件**: `src/configs/models/high_recall_optimized.json`

### 核心成功要素
1. ✅ 原始EPMMOENet架构
2. ✅ 单任务学习模式  
3. ✅ 经典focal loss参数
4. ✅ 月份权重[5.0, 2.0, 2.0, 2.0, 1.0, 1.0]
5. ✅ 关键意向特征全覆盖

---

**文档创建**: 2025-06-15  
**最后更新**: 2025-06-15  
**当前最优**: PR_AUC=0.1894, Recall@840=55.46%  
**下次更新**: 每次实验后及时更新