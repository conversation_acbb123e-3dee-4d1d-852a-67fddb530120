# 代码整理计划

## 文件分类

### 🟢 保留 - 核心功能代码
- `src/features/modern_pipeline.py` - 现代化特征工程管道 (核心)
- `src/models/modern_model_builder.py` - 现代化模型构建器 (核心)
- `data/data_analyze.py` - 数据分析工具 (已迁移，保留)

### 🟡 合并 - 相似功能脚本  
- `scripts/modern_training.py` - 现代化训练脚本 (保留为主)
- `scripts/benchmark_training.py` - 基准训练脚本 (功能合并到modern_training)
- `scripts/simple_multi_epoch.py` - 简单多轮训练 (功能合并)

### 🔴 删除 - 临时测试代码
- `scripts/multi_epoch_training.py` - 临时测试脚本
- `scripts/extended_training.py` - 扩展测试脚本  
- `scripts/feature_usage_audit.py` - 特征审查脚本 (已完成任务)
- `scripts/business_logic_reorganize.py` - 临时重组脚本
- `scripts/single_dataset_reorganize.py` - 临时重组脚本
- `scripts/train_business_logic.py` - 临时训练脚本
- `scripts/train_with_reorganized_data.py` - 临时训练脚本

### 📋 文档整理
- `data/DATA_STRUCTURE_ANALYSIS.md` - 保留
- `data/FEATURE_ANALYSIS_DETAILED.md` - 保留
- `data/MODEL_BEHAVIOR_ANALYSIS.md` - 保留  
- `data/IMPROVEMENT_ROADMAP.md` - 保留
- `CLAUDE.md` - 保留并更新

## 目录结构优化建议

```
nio-eatv/
├── src/
│   ├── features/
│   │   ├── modern_pipeline.py      # 现代化特征工程管道
│   │   └── feature_engineering.py  # 重命名并整合其他特征工具
│   ├── models/  
│   │   ├── modern_model_builder.py # 现代化模型构建器
│   │   └── architectures/          # 模型架构定义
│   └── training/
│       └── modern_trainer.py       # 统一训练脚本
├── scripts/
│   ├── train.py                    # 主训练入口 (整合modern_training)
│   └── evaluate.py                 # 模型评估脚本
├── data/
│   ├── analysis/                   # 数据分析相关
│   │   ├── data_analyze.py
│   │   └── reports/               # 分析报告
│   └── docs/                      # 数据文档
└── docs/
    ├── DEVELOPMENT_GUIDE.md       # 开发指南
    ├── API_REFERENCE.md           # API参考
    └── TRAINING_GUIDE.md          # 训练指南
```