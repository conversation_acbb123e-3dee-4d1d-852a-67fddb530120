# NIO转化率预测模型 - 开发指南

## 📁 项目结构

```
nio-eatv/
├── src/                           # 核心源代码
│   ├── features/
│   │   └── modern_pipeline.py    # 现代化特征工程管道
│   ├── models/
│   │   └── modern_model_builder.py # 现代化模型构建器
│   ├── data/
│   │   ├── loader.py              # 数据加载器
│   │   └── preprocessor.py        # 数据预处理
│   └── configs/
│       └── unified_config_manager.py # 配置管理
├── scripts/
│   ├── train.py                   # 统一训练脚本 (主入口)
│   ├── modern_training.py         # 现代化训练脚本 (弃用)
│   ├── benchmark_training.py      # 基准训练脚本 (弃用)
│   └── simple_multi_epoch.py      # 简单训练脚本 (保留)
├── data/
│   ├── analysis/
│   │   ├── data_analyze.py        # 数据分析工具
│   │   └── reports/              # 分析报告
│   ├── DATA_STRUCTURE_ANALYSIS.md # 数据结构分析
│   ├── FEATURE_ANALYSIS_DETAILED.md # 特征详细分析
│   ├── MODEL_BEHAVIOR_ANALYSIS.md # 模型行为分析
│   └── IMPROVEMENT_ROADMAP.md     # 改进路线图
├── logs/                          # 训练日志和模型输出
└── docs/                          # 项目文档
    ├── DEVELOPMENT_GUIDE.md       # 开发指南 (本文档)
    ├── API_REFERENCE.md           # API参考
    └── TRAINING_GUIDE.md          # 训练指南
```

## 🚀 快速开始

### 环境设置
```bash
# 安装依赖
pip install tensorflow==2.19.0 pandas numpy pyyaml

# 设置Python路径
export PYTHONPATH="${PYTHONPATH}:$(pwd)"
```

### 训练模型
```bash
# 快速测试 (仅验证管道)
python scripts/train.py --mode test

# 现代化模型训练
python scripts/train.py --mode modern --epochs 10

# 简化模型训练 
python scripts/train.py --mode simple --epochs 10

# 对比训练 (训练多个模型对比)
python scripts/train.py --mode compare --epochs 5
```

## 🧩 核心组件

### 1. 现代化特征工程管道 (`src/features/modern_pipeline.py`)

**主要功能:**
- 智能特征类型推断 (Bucket/Categorical/Sequence)
- 配置驱动的特征处理
- 业务规则缺失值处理
- 自动分桶和编码

**使用示例:**
```python
from src.features.modern_pipeline import ModernFeaturePipeline

# 创建管道
pipeline = ModernFeaturePipeline()
pipeline.load_config_from_yaml("sample_20250311_v7-20250311")

# 拟合和转换
pipeline.fit(df_train)
train_features = pipeline.transform(df_train)
```

### 2. 现代化模型构建器 (`src/models/modern_model_builder.py`)

**主要功能:**
- Cross-Net + Multi-Head Attention架构
- Focal Loss处理不平衡数据
- 自适应模型配置
- 完整的训练回调

**使用示例:**
```python
from src.models.modern_model_builder import ModernModelBuilder, ModelConfig

# 创建配置
config = ModelConfig(
    use_cross_net=True,
    use_attention=True,
    use_focal_loss=True
)

# 构建模型
builder = ModernModelBuilder(config)
model = builder.build_model(pipeline)
```

### 3. 统一训练脚本 (`scripts/train.py`)

**主要功能:**
- 多种训练模式 (test/modern/simple/compare)
- 完整的训练流程
- 自动报告生成
- 性能对比

## 🔧 配置管理

### 特征配置
特征配置位于 `src/configs/experiments/sample_20250311_v7-20250311.yaml`

**特征类型:**
- `Bucket`: 数值特征，自动分桶处理
- `StringLookup`: 类别特征，embedding编码
- `Sequence`: 序列特征，GRU处理

### 模型配置
```python
ModelConfig(
    output_dim=6,              # 输出维度 (6个月)
    hidden_units=[512,256,128], # 隐藏层单元
    use_cross_net=True,        # 使用Cross Network
    use_attention=True,        # 使用注意力机制
    use_focal_loss=True,       # 使用Focal Loss
    dropout_rate=0.3,          # Dropout率
    learning_rate=0.001        # 学习率
)
```

## 📊 性能监控

### 关键指标
- **PR-AUC**: 主要评估指标 (适合不平衡数据)
- **Precision/Recall**: 精确率和召回率
- **Loss**: 训练和验证损失

### 训练报告
训练完成后会生成JSON格式的详细报告，包含:
- 模型配置
- 训练历史
- 性能指标
- 特征统计

## 🐛 常见问题

### 1. ShuffleDatasetV3缓冲区填充慢
**问题**: 训练开始前长时间显示 "Filling up shuffle buffer"
**解决**: 已在 `create_dataset_from_pipeline` 中自动调整缓冲区大小

### 2. 特征类型不匹配
**问题**: "Cast string to float is not supported"  
**解决**: 使用现代化管道确保类型一致性

### 3. 内存不足
**问题**: 训练时内存溢出
**解决**: 减小 `batch_size` 或使用简化模型

### 4. 训练速度慢
**问题**: 现代化模型参数多 (2500万)
**解决**: 使用 `--mode simple` 或减少 `epochs`

## 🔄 开发工作流

### 1. 数据探索
```bash
python data/analysis/data_analyze.py
```

### 2. 特征工程
```bash
# 测试特征管道
python scripts/train.py --mode test
```

### 3. 模型训练
```bash
# 快速验证
python scripts/train.py --mode simple --epochs 3

# 完整训练
python scripts/train.py --mode modern --epochs 15
```

### 4. 性能对比
```bash
# 对比不同模型
python scripts/train.py --mode compare --epochs 5
```

## 📈 性能基准

### 当前基准 (2025-06-14)

| 模型类型 | 参数数量 | PR-AUC | 训练时间 | 特征数 |
|----------|----------|--------|----------|--------|
| Simple | ~150万 | 0.15+ | ~30秒/10epoch | 355 |
| Modern | ~2500万 | 0.18+ | ~5分钟/3epoch | 355 |

### 改进目标
- PR-AUC > 0.20
- 保持训练效率
- 完整特征利用

## 🚨 注意事项

1. **数据一致性**: 确保训练和测试数据的时间窗口正确
2. **特征对齐**: 所有特征必须与配置文件匹配
3. **内存管理**: 监控训练时的内存使用
4. **版本控制**: 重要更改前创建分支
5. **日志保存**: 训练日志和模型自动保存到 `logs/` 目录

## 📞 支持

如有问题请查看:
1. `logs/` 目录中的训练日志
2. `data/` 目录中的分析报告  
3. 本文档的常见问题部分

---

**最后更新**: 2025-06-14  
**版本**: v2.0  
**维护者**: ML团队