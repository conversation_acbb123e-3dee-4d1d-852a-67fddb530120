# instruct.md: Plan to Optimize Conversion Prediction Model for Recall

**Goal:** Systematically improve the Recall metric (especially <PERSON><PERSON><PERSON>@840 and overall Recall on the evaluation set, minimizing False Negatives) for the conversion prediction model (`EPMMOENet`), addressing the observed time decay and generalization issues.

**Methodology:** Implement changes incrementally, evaluating the impact on Recall (and other relevant metrics like PR AUC and Precision@840) on the designated evaluation dataset (`evaluate_file`) after each significant step. Track performance changes across months (`metrics_month_evaluate`).

---

## Step 0: Establish Baseline & Setup

**Objective:** Record the current model's performance accurately and ensure a reproducible starting point.

**Actions:**
1.  **Run Baseline Evaluation:** Execute the current training and evaluation pipeline using the exact code and configuration that produced the `sample_20250311_v7-20250311_metrics.json` results. Use the command from the README:
    ```bash
    # Example command - adjust parameters as needed for the original run
    python src/train.py --model_code=sample_20250311_v7-20250311 --dataset_code=dataset_nio_new_car_v15 --evaluate_file="20240531_随机采样1%.parquet" --data_dir="data" --epochs=50 --patience=10 # Add other relevant args used previously
    ```
2.  **Record Baseline Metrics:** Carefully document the Recall@840 and overall Recall (if calculated) for *each month* and the *overall* `metrics_evaluate` from the newly generated evaluation output JSON. Let's denote the overall baseline Recall@840 as `[Baseline Recall@840]`.
3.  **Version Control:** Ensure the current state of the code (especially `src/`) and configuration (`src/configs/`) is committed to Git or saved reliably.

---

## Step 1: Address Class Imbalance (Highest Priority for Recall)

**Objective:** Make the model more sensitive to the minority positive class (conversions) to directly improve Recall.

**Actions (Choose 1a or 1b initially, or combine later):**

*   **1a. Modify Loss Function:**
    *   **Locate:** Find where the loss function is defined and applied (likely in `src/training/losses.py` and used within `src/training/trainer.py`).
    *   **Option 1 (Weighted Cross-Entropy):**
        *   Calculate class weights inversely proportional to their frequency in the training dataset.
        *   Modify the training script or trainer class to use `tf.keras.losses.BinaryCrossentropy` with the `class_weight` parameter passed during `model.compile()` or applied directly in a custom training loop.
    *   **Option 2 (Focal Loss):**
        *   Find or implement a TensorFlow version of Focal Loss (check `tensorflow-addons` or implement based on the paper).
        *   Replace the current loss function with Focal Loss. Tune its hyperparameters (`alpha` for weighting, `gamma` for focusing).
    *   **Retrain & Evaluate:** Retrain the model using *only* the modified loss function. Compare Recall metrics against the baseline.

*   **1b. Implement Data Sampling:**
    *   **Locate:** Find where the `tf.data.Dataset` objects are created for training (likely in `src/features/builder.py` or `src/training/trainer.py`).
    *   **Implement Oversampling:** Modify the dataset pipeline to oversample the positive class examples. Techniques like `tf.data.experimental.sample_from_datasets` can be used to sample from positive and negative examples with different ratios to create more balanced batches.
    *   **Retrain & Evaluate:** Retrain the model using *only* the oversampling technique. Compare Recall metrics against the baseline.

---

## Step 2: Optimize Sequence Modeling & Attention

**Objective:** Ensure the sequence processing components are optimally configured to capture conversion-related patterns without being overly sensitive to noise or excessively discarding older information.

**Actions:**

1.  **Tune TimeSeriesAttention:**
    *   **Locate:** The `time_decay_factor` is passed during `EPMMOENet_Model` instantiation (likely in `src/training/trainer.py` or `src/train.py`).
    *   **Experiment:** Run training experiments varying this factor. Try values like `0.01`, `0.1`, `0.5`, and potentially `0` (disabling explicit decay).
    *   **Evaluate:** Compare Recall for each factor against the best result from Step 1.
2.  **Tune GRU Dimension:**
    *   **Locate:** The `default_gru_dimension` and potentially sequence-specific `gru_dimension` are used in `EPMMOENet_Model.__init__`. These might be configurable via the model JSON config or passed during instantiation.
    *   **Experiment:** Try different dimensions (e.g., smaller like 16, larger like 64 or 128).
    *   **Evaluate:** Compare Recall.

---

## Step 3: Feature Engineering Review & Enhancement

**Objective:** Improve the quality and relevance of features, especially those capturing time dynamics and conversion intent.

**Actions:**

1.  **Analyze Feature Importance (Optional but Recommended):**
    *   **Implement:** If possible, add code to `src/evaluation/evaluator.py` or a separate script to calculate feature importances (e.g., permutation importance) on the *best performing model so far*, specifically looking at features driving positive predictions.
2.  **Create/Refine Time-Aware Features:**
    *   **Locate:** Modify `src/data/preprocessor.py` or `src/features/builder.py`.
    *   **Implement:** Add features like:
        *   Time since last key event (e.g., last visit, last configuration).
        *   Frequency of key events within different time windows (last 7 days, 30 days).
        *   Interaction recency features.
    *   **Update Config:** Add new features to the relevant dataset configuration file (`src/configs/datasets/...`).
    *   **Retrain & Evaluate:** Compare Recall.
3.  **Review Existing Feature Preprocessing:**
    *   **Locate:** Check `src/data/preprocessor.py` and model config (`src/configs/models/...`).
    *   **Verify:** Ensure robust handling of categorical features (vocabulary size, unknown tokens), appropriate scaling/bucketing of numerical features, and correct handling of sequence padding/masking.

---

## Step 4: Tune Model Complexity & Regularization

**Objective:** Ensure the model complexity is appropriate for the data and task, mitigating potential overfitting which could harm generalization and Recall on new data.

**Actions:**

1.  **Adjust Tower Layers:**
    *   **Locate:** Modify `Tower_layers` within `src/models/networks/EPMMOENet.py`.
    *   **Experiment:** Try reducing/increasing the number of layers or neurons per layer. Adjust `Dropout` rates.
    *   **Evaluate:** Compare Recall and the gap between training and evaluation performance.
2.  **Tune Regularization:**
    *   **Locate:** Adjust `l2` regularization values in `Dense` layers within `EPMMOENet.py`.
    *   **Experiment:** Try different L2 strengths (e.g., 0.001, 0.05).
    *   **Evaluate:** Compare Recall.
3.  **Evaluate `CrossLayer` Impact:**
    *   **Locate:** The `use_cross_layer` argument is likely passed in `src/train.py` or configured elsewhere.
    *   **Experiment:** Train a variant with `use_cross_layer=False`.
    *   **Evaluate:** Does removing it improve generalization and Recall on the evaluation set, even if training performance drops slightly?

---

## Step 5: Evaluate Simpler Baselines (If Necessary)

**Objective:** Determine if the complexity of the current architecture is justified or if simpler models perform adequately after feature improvements.

**Actions:**

1.  **Implement Baselines:** Train simpler models like Logistic Regression or Gradient Boosting (XGBoost/LightGBM) using the *same set of processed features* generated by your pipeline (up to the point before they enter `EPMMOENet`).
2.  **Compare:** Evaluate their Recall performance against the best `EPMMOENet` variant. This provides context on whether architectural complexity or feature quality is the main driver/limiter.

---

## Step 6: Continuous Evaluation & Iteration

**Objective:** Track progress systematically and combine beneficial changes.

**Actions:**

1.  **Evaluate Rigorously:** After *each significant change* (e.g., changing loss function, adding major features), run the full evaluation on the held-out `evaluate_file` and analyze the monthly Recall trend.
2.  **Document:** Keep clear records of experiments, configurations, and resulting metrics.
3.  **Iterate:** Combine changes that demonstrably improved Recall without unacceptable drops in other key metrics. Be prepared to revert changes that harm performance on the evaluation set. Adjust decision thresholds if needed based on the Recall/Precision trade-off acceptable for the business.

---