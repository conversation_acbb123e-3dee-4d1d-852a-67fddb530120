# 开发指南

## 🔧 环境设置

### 依赖要求
```bash
# Python 3.8+
pip install tensorflow==2.19.0
pip install pandas numpy pyyaml
```

### 项目启动检查
```bash
# 快速环境验证
python scripts/unified_training_test.py --mode simple

# 完整系统验证  
python scripts/unified_training_test.py --mode real
```

## 🏗️ 代码架构

### 核心模块

**配置管理** (`src/configs/`)
- `unified_config_manager.py` - 统一配置管理器
- `experiments/` - 实验配置文件（YAML格式）
- `datasets/` - 数据集配置文件

**模型层** (`src/models/`)
- `adaptive_model_factory.py` - 自适应模型工厂
- `networks/EPMMOENet_enhanced.py` - 统一模型架构
- `layers/layers.py` - 自定义网络层

**数据处理** (`src/data/`, `src/features/`)
- `loader.py` - 数据加载器
- `preprocessor.py` - 数据预处理
- `builder.py` - 特征构建器

### 开发模式

**1. 快速实验**
```python
from src.models.adaptive_model_factory import adaptive_factory

# 自动构建最优模型（推荐）
model, preprocessing_info = adaptive_factory.create_model_from_data(
    train_data=df,
    target_column="m_purchase_days_nio_new_car"
)
```

**2. 配置驱动开发**
```python
from src.configs.unified_config_manager import unified_config_manager

# 加载实验配置
config = unified_config_manager.load_experiment_config("sample_20250311_v7-20250311")
```

**3. 传统训练方式**
```bash
python src/train.py --model_code=experiment_name --dataset_code=dataset_name
```

## 🧪 测试和调试

### 单元测试
```bash
# 运行所有测试
python -m pytest tests/

# 测试特定模块
python -m pytest tests/test_adaptive_factory.py
```

### 调试工具
```bash
# 详细日志输出
export PYTHONPATH="${PYTHONPATH}:$(pwd)"
python scripts/unified_training_test.py --mode real --verbose

# 检查配置有效性
python -c "
from src.configs.unified_config_manager import unified_config_manager
config = unified_config_manager.load_experiment_config('sample_20250311_v7-20250311')
print(f'Loaded {len(config.features)} features')
"
```

## 📝 代码规范

### 模型开发
1. **新模型架构**：在`src/models/networks/`中添加
2. **自定义层**：在`src/models/layers/`中实现
3. **配置格式**：统一使用YAML格式

### 特征工程
1. **特征分组**：使用业务逻辑分组特征
2. **类型推断**：利用自适应工厂的智能推断
3. **业务分箱**：保留专业的业务知识

### 配置管理
1. **分层配置**：基础→业务→实验
2. **向后兼容**：保持现有API不变
3. **文档化**：为配置添加说明注释

## 🚀 性能优化

### 训练优化
```python
# 使用混合精度训练
policy = tf.keras.mixed_precision.Policy('mixed_float16')
tf.keras.mixed_precision.set_global_policy(policy)

# GPU内存增长设置
gpus = tf.config.experimental.list_physical_devices('GPU')
if gpus:
    for gpu in gpus:
        tf.config.experimental.set_memory_growth(gpu, True)
```

### 数据优化
```python
# 优化数据类型
df = df.astype({
    'user_id': 'category',
    'numerical_feature': 'float32'
})

# 批处理优化
dataset = dataset.batch(256).prefetch(tf.data.AUTOTUNE)
```

## 🔍 常见问题

### Q1: 训练时出现NaN损失
**解决方案**：
1. 降低学习率到0.0001
2. 检查特征预处理是否正确
3. 使用梯度裁剪

### Q2: 特征配置不匹配
**解决方案**：
1. 使用自适应模型工厂自动匹配
2. 检查配置文件中的特征名称
3. 验证数据列名与配置一致

### Q3: 内存不足
**解决方案**：
1. 减少batch_size
2. 使用数据采样进行快速验证
3. 优化数据类型（float32而非float64）

## 📊 监控和日志

### 日志配置
所有日志统一输出到`logs/`目录：
- 训练日志：`logs/unified_training_*.log`
- 训练报告：`logs/unified_training_report_*.json`

### 性能监控
```python
# 训练过程监控
history = model.fit(
    train_dataset,
    validation_data=test_dataset,
    callbacks=[
        tf.keras.callbacks.EarlyStopping(patience=5),
        tf.keras.callbacks.ReduceLROnPlateau(patience=3)
    ]
)
```

## 🔄 发布流程

### 1. 本地验证
```bash
# 完整测试流程
python scripts/unified_training_test.py --mode simple
python scripts/unified_training_test.py --mode real
```

### 2. 代码检查
```bash
# 代码格式检查
black src/ scripts/
flake8 src/ scripts/

# 类型检查（如果使用）
mypy src/
```

### 3. 集成测试
```bash
# 端到端测试
python src/train.py --model_code=sample_20250311_v7-20250311 --epochs=1 --batch_size=256
```

## 🎯 最佳实践

1. **优先使用自适应模型工厂**：减少配置错误，提升开发效率
2. **保留业务知识**：不要完全依赖自动化，保留关键业务分箱
3. **日志记录**：重要操作都要有日志记录
4. **配置版本化**：使用git管理配置文件变更
5. **渐进式优化**：先保证功能正确，再优化性能

---

*遇到问题时，优先检查logs目录下的详细日志输出。*